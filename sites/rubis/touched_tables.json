["tabDelivery Note", "tabPurchase Invoice", "tabDelivery", "tabDocType State", "tabPortal", "tabPatch Log", "tabPatch", "tabDocType Link", "tabStock", "tabEmployee Salary Component Limit", "tabPurchase Receipt", "tabJob <PERSON>", "tabPurchase", "tabEmployee", "tabJob", "tabStock Reconciliation", "tabRoute", "tabProperty", "tabDocPerm", "tabVehicle Service", "tabStock Entry", "tabInstalled", "tabDocType", "tabPayment Gateway Account", "tabPOS Invoice", "tabDocType Action", "tabBOM Scrap Item", "tabVehicle", "tabPayment", "tabBOM", "tabJob Card Time Log", "tabCustom Field", "tabInstalled Application", "tabDeleted Document", "tabBOM Operation", "tabDeleted", "tabVehicle Log", "tabSalary Slip OT Component", "tabPortal Menu Item", "tabPrint Heading", "tabComment", "tabEmployee OT Component", "tabRoute History", "tabProperty Setter", "tabJob <PERSON>", "tabTag", "tabSingle", "tabSingle Piecework Employees", "tabPOS", "tabCustom", "tabSales", "tabSalary", "tabSingles", "tabDocField", "tabSales Invoice Item", "tabOperation", "tabEmployee Piecework Additional Salary", "tabSales Invoice", "tabTag Link", "tabPrint"]