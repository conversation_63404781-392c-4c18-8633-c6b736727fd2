2025-08-12 17:02:13,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0
2025-08-12 17:02:13,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `source_warehouse` varchar(140), ADD COLUMN `fg_warehouse` varchar(140), ADD COLUMN `wip_warehouse` varchar(140), ADD COLUMN `scrap_warehouse` varchar(140)
2025-08-12 17:02:13,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-08-12 17:02:13,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` ADD COLUMN `import_file` varchar(140)
2025-08-12 17:02:13,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-08-12 17:02:13,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` ADD COLUMN `item` varchar(140)
2025-08-12 17:02:13,776 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-08-12 17:02:14,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `old_employee_id` varchar(140), ADD COLUMN `heslb_f4_index_number` varchar(140)
2025-08-12 17:02:14,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-08-12 17:02:15,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-08-12 17:02:15,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0
2025-08-12 17:02:15,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-08-12 17:02:15,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-08-12 17:02:15,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `csf_tz_is_auto_close_dn` int(1) not null default 0, ADD COLUMN `csf_tz_close_dn_after` int(11) not null default 0
2025-08-12 17:02:15,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-08-12 17:02:16,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0, ADD COLUMN `no_of_hours` decimal(21,9) not null default 0, ADD COLUMN `auto_repeat_frequency` varchar(140), ADD COLUMN `auto_repeat_end_date` date, ADD COLUMN `last_transaction_amount` decimal(21,9) not null default 0, ADD COLUMN `last_transaction_date` date, ADD COLUMN `auto_created_based_on` varchar(140)
2025-08-12 17:02:16,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-08-12 17:02:16,490 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-08-12 17:02:17,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `is_authotp_applied` int(1) not null default 0, ADD COLUMN `default_authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-08-12 17:02:17,211 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-08-12 17:02:17,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-08-12 17:02:17,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0
2025-08-12 17:02:17,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-08-12 17:02:17,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-08-12 17:02:17,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-08-12 17:02:17,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-08-12 17:02:17,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0
2025-08-12 17:02:17,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-08-12 17:02:18,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `custom_beneficiary_bank_bic` varchar(140), ADD COLUMN `custom_employee_country` varchar(140), ADD COLUMN `custom_bank_account_name` varchar(140), ADD COLUMN `custom_bank_country_code` varchar(140), ADD COLUMN `custom_employee_country_code` varchar(140)
2025-08-12 17:02:18,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-08-12 17:02:18,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-08-12 17:02:18,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-08-12 17:02:18,625 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-08-12 17:02:19,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-08-12 17:02:19,237 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-08-12 17:02:19,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-08-12 17:02:19,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0
2025-08-12 17:02:19,441 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-08-12 17:02:19,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-08-12 17:02:19,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-08-12 17:02:19,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-08-12 17:02:19,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-08-12 17:02:19,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-08-12 17:02:20,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0
2025-08-12 17:02:20,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-08-12 17:02:20,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-08-12 17:02:20,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-08-12 17:02:20,608 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-08-12 17:02:20,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-08-12 17:02:20,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-08-12 17:02:20,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-08-12 17:02:20,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-08-12 17:02:21,092 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0
2025-08-12 17:02:21,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-08-12 17:02:21,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-08-12 17:02:22,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `create_cash_journal` int(1) not null default 0, ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0
2025-08-12 17:02:22,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-08-12 17:02:22,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0
2025-08-12 17:02:22,991 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `pension_fund` varchar(140), ADD COLUMN `pension_fund_number` varchar(140), ADD COLUMN `wcf_number` varchar(140), ADD COLUMN `tin_number` varchar(140), ADD COLUMN `national_identity` varchar(140), ADD COLUMN `bank_code` varchar(140)
2025-08-12 17:02:23,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-08-12 17:02:23,144 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` ADD COLUMN `applicable_charges_per_item` decimal(21,9) not null default 0, ADD COLUMN `price_per_item` decimal(21,9) not null default 0
2025-08-12 17:02:23,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` MODIFY `applicable_charges` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-08-12 17:02:23,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` ADD COLUMN `bank_supplier` varchar(140)
2025-08-12 17:09:35,307 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-12 17:09:35,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart` ADD COLUMN `show_values_over_chart` int(1) not null default 0
2025-08-12 17:09:38,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-12 17:09:38,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `advance_voucher_type` varchar(140), ADD COLUMN `advance_voucher_no` varchar(140)
2025-08-12 17:09:38,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-08-12 17:09:39,299 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-08-12 17:09:39,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Ledger Entry` MODIFY `amount_in_account_currency` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-08-12 17:09:40,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-08-12 17:09:40,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Gateway Account` ADD COLUMN `company` varchar(140)
2025-08-12 17:09:41,735 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-08-12 17:09:41,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` ADD COLUMN `advance_voucher_type` varchar(140), ADD COLUMN `advance_voucher_no` varchar(140)
2025-08-12 17:09:41,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `debit` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0, MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0
2025-08-12 17:09:42,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` ADD COLUMN `delinked` int(1) not null default 0
2025-08-12 17:09:42,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` MODIFY `amount` decimal(21,9) not null default 0
2025-08-12 17:09:42,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` ADD INDEX `creation`(`creation`)
2025-08-12 17:09:43,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-08-12 17:09:43,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-12 17:09:44,373 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` MODIFY `hour_rate_electricity` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `hour_rate_consumable` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `hour_rate_labour` decimal(21,9) not null default 0, MODIFY `hour_rate_rent` decimal(21,9) not null default 0
2025-08-12 17:09:44,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Operation` MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `cost_per_unit` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_cost_per_unit` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0
2025-08-12 17:09:44,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Scrap Item` MODIFY `stock_qty` decimal(21,9) not null default 0
2025-08-12 17:09:45,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` MODIFY `total_time_in_mins` decimal(21,9) not null default 0, MODIFY `time_required` decimal(21,9) not null default 0, MODIFY `for_quantity` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0
2025-08-12 17:09:45,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Scrap Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-08-12 17:09:45,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `batch_size` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0
2025-08-12 17:09:45,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Time Log` MODIFY `time_in_mins` decimal(21,9) not null default 0
2025-08-12 17:09:45,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` MODIFY `total_operation_time` decimal(21,9) not null default 0
2025-08-12 17:09:46,197 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0
2025-08-12 17:09:46,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-08-12 17:09:46,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD COLUMN `delivered_by_supplier` int(1) not null default 0
2025-08-12 17:09:46,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0
2025-08-12 17:09:47,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-08-12 17:09:48,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` ADD COLUMN `customer` varchar(140)
2025-08-12 17:09:48,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` MODIFY `purchase_rate` decimal(21,9) not null default 0
2025-08-12 17:09:48,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-08-12 17:09:48,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD INDEX `inter_company_reference_index`(`inter_company_reference`)
2025-08-12 17:09:49,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation` MODIFY `difference_amount` decimal(21,9) not null default 0
2025-08-12 17:09:49,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `buying_price_list` varchar(140)
2025-08-12 17:09:49,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-08-12 17:09:49,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-08-12 17:09:50,599 WARNING database DDL Query made to DB:
create table `tabSDG Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`no_of_employees` int(11) not null default 0,
`sector` varchar(140),
`location` varchar(140),
`organization_type` varchar(140),
`legal_structure` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 17:09:50,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabGRI Standard` ADD COLUMN `disclosure` varchar(140), ADD COLUMN `location` varchar(140)
2025-08-12 17:09:50,834 WARNING database DDL Query made to DB:
ALTER TABLE `tabGRI Standard` ADD UNIQUE INDEX IF NOT EXISTS standard_code (`standard_code`)
2025-08-12 17:09:50,972 WARNING database DDL Query made to DB:
create table `tabESG Reporting Framework` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`reporting_cycle` varchar(140),
`first_reporting_year` int(11) not null default 0,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 17:09:51,117 WARNING database DDL Query made to DB:
create table `tabReporting Standard Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`standard` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 17:09:51,246 WARNING database DDL Query made to DB:
create table `tabOperational Footprint Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140),
`facilty_name` varchar(140),
`location` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 17:09:51,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-12 17:09:51,529 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-08-12 17:09:51,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-08-12 17:09:51,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-08-12 17:09:53,589 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-12 17:09:53,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-12 17:09:54,844 WARNING database DDL Query made to DB:
truncate `tabAdvance Payment Ledger Entry`
2025-08-12 17:10:00,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-08-12 17:10:00,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0
2025-08-12 17:10:00,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-08-12 17:10:00,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-08-12 17:10:00,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-08-12 17:10:00,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-08-12 17:10:00,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-08-12 17:10:00,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
