["tabJournal Entry Account", "tabPayment Entry Reference", "tabBOM", "tabReport", "tabSales", "tabPortal Menu Item", "tabPortal", "tabSales Order", "tabGRI", "tabStock Reconciliation", "tabAdvance Payment Ledger Entry", "tabPOS Invoice", "tabInstalled Application", "tabPayment Ledger Entry", "tabDashboard", "tabPurchase Receipt", "tabAdvance", "tabPrint Heading", "tabWorkstation", "tabPacked", "tabOperation", "tabSingles", "tabVehicle", "tabPatch Log", "tabAsset Repair", "tabTag Link", "tabBOM Scrap Item", "tabJob <PERSON>", "tabPOS", "tabReport Column", "tabSerial", "tabJob", "tabProperty Setter", "tabDocType State", "tabWeb Form Field", "tabProperty", "tabRoute", "tabDelivery Note", "tabStock Entry", "tabStock", "tabSerial No", "tabCompany", "tabPayment Gateway Account", "tabMaterial Request", "tabDocType Link", "tabGRI Standard", "tabDashboard Chart", "tabVehicle Service", "tabPacked <PERSON>em", "tabCustom", "tabPrint", "tabHas", "tabRoute History", "tabTag", "tabDeleted", "tabPurchase", "tabInstalled", "tabSales Invoice Item", "tabJob Card Time Log", "tabDocType", "tabWeb Form", "tabSales Invoice", "tabDocField", "tabWork", "tabPick", "tabComment", "tabDocType Action", "tabPick List", "tabCustom Field", "tabAsset", "tabWeb", "tabWork Order Operation", "tabBOM Operation", "tabPatch", "tabDeleted Document", "tabHas Role", "tabDocPerm", "tabJournal", "tabDelivery", "tabWeb Form List Column", "tabPayment", "tabReport Filter", "tabMaterial", "tabVehicle Log", "tabJob <PERSON>", "tabPurchase Invoice"]