{"billing.bundle.js": "/assets/frappe/dist/js/billing.bundle.3M2NKQ7F.js", "bootstrap-4-web.bundle.js": "/assets/frappe/dist/js/bootstrap-4-web.bundle.AZ67VXZX.js", "controls.bundle.js": "/assets/frappe/dist/js/controls.bundle.H366FK6P.js", "data_import_tools.bundle.js": "/assets/frappe/dist/js/data_import_tools.bundle.F7I46Y5V.js", "desk.bundle.js": "/assets/frappe/dist/js/desk.bundle.J33E3CWY.js", "dialog.bundle.js": "/assets/frappe/dist/js/dialog.bundle.X7QCPWYD.js", "form.bundle.js": "/assets/frappe/dist/js/form.bundle.ZD572PGM.js", "frappe-web.bundle.js": "/assets/frappe/dist/js/frappe-web.bundle.4XKJFVOE.js", "libs.bundle.js": "/assets/frappe/dist/js/libs.bundle.LLRFRX7M.js", "list.bundle.js": "/assets/frappe/dist/js/list.bundle.WOFYL4DO.js", "logtypes.bundle.js": "/assets/frappe/dist/js/logtypes.bundle.MJKW7EK3.js", "onboarding_tours.bundle.js": "/assets/frappe/dist/js/onboarding_tours.bundle.P7QYMXLW.js", "report.bundle.js": "/assets/frappe/dist/js/report.bundle.J27MFJ77.js", "sentry.bundle.js": "/assets/frappe/dist/js/sentry.bundle.SI3DB3BY.js", "telemetry.bundle.js": "/assets/frappe/dist/js/telemetry.bundle.ZJBT5ETW.js", "user_profile_controller.bundle.js": "/assets/frappe/dist/js/user_profile_controller.bundle.TAMQL3L3.js", "video_player.bundle.js": "/assets/frappe/dist/js/video_player.bundle.IOEIXC2G.js", "web_form.bundle.js": "/assets/frappe/dist/js/web_form.bundle.3OXIHQKM.js", "form_builder.bundle.js": "/assets/frappe/dist/js/form_builder.bundle.YRYSA3IK.js", "print_format_builder.bundle.js": "/assets/frappe/dist/js/print_format_builder.bundle.23LHQCRJ.js", "workflow_builder.bundle.js": "/assets/frappe/dist/js/workflow_builder.bundle.L3TJGBVY.js", "build_events.bundle.js": "/assets/frappe/dist/js/build_events.bundle.RMXS47QW.js", "file_uploader.bundle.js": "/assets/frappe/dist/js/file_uploader.bundle.NJ4L752J.js", "kanban_board.bundle.js": "/assets/frappe/dist/js/kanban_board.bundle.OUFA2R27.js", "desk.bundle.css": "/assets/frappe/dist/css/desk.bundle.355WKX2S.css", "email.bundle.css": "/assets/frappe/dist/css/email.bundle.4TYXGZLT.css", "login.bundle.css": "/assets/frappe/dist/css/login.bundle.5Z5WGW2C.css", "print.bundle.css": "/assets/frappe/dist/css/print.bundle.NJIJOXWZ.css", "print_format.bundle.css": "/assets/frappe/dist/css/print_format.bundle.N6REEFDH.css", "report.bundle.css": "/assets/frappe/dist/css/report.bundle.HSU7DIAF.css", "web_form.bundle.css": "/assets/frappe/dist/css/web_form.bundle.3AQ4MY4L.css", "website.bundle.css": "/assets/frappe/dist/css/website.bundle.DFHHW5ZQ.css", "bank-reconciliation-tool.bundle.js": "/assets/erpnext/dist/js/bank-reconciliation-tool.bundle.G7MHMAFO.js", "erpnext-web.bundle.js": "/assets/erpnext/dist/js/erpnext-web.bundle.253I7LT4.js", "erpnext.bundle.js": "/assets/erpnext/dist/js/erpnext.bundle.ZTOUMAFX.js", "item-dashboard.bundle.js": "/assets/erpnext/dist/js/item-dashboard.bundle.Q4W2MCOH.js", "point-of-sale.bundle.js": "/assets/erpnext/dist/js/point-of-sale.bundle.LZTBK57Q.js", "bom_configurator.bundle.js": "/assets/erpnext/dist/js/bom_configurator.bundle.6NZSNVVE.js", "erpnext-web.bundle.css": "/assets/erpnext/dist/css/erpnext-web.bundle.HLMYADGF.css", "erpnext.bundle.css": "/assets/erpnext/dist/css/erpnext.bundle.KUIVMGU7.css", "erpnext_email.bundle.css": "/assets/erpnext/dist/css/erpnext_email.bundle.CS2WYKZH.css", "hierarchy-chart.bundle.js": "/assets/hrms/dist/js/hierarchy-chart.bundle.34LONOHB.js", "hrms.bundle.js": "/assets/hrms/dist/js/hrms.bundle.KYSDSR2P.js", "interview.bundle.js": "/assets/hrms/dist/js/interview.bundle.IUBSPKDA.js", "performance.bundle.js": "/assets/hrms/dist/js/performance.bundle.5KYJU75D.js", "hrms.bundle.css": "/assets/hrms/dist/css/hrms.bundle.XESYCNXB.css", "wiki.bundle.js": "/assets/wiki/dist/js/wiki.bundle.RANUF4YG.js", "contributions.bundle.css": "/assets/wiki/dist/css/contributions.bundle.WZIYFRFL.css", "edit_wiki.bundle.css": "/assets/wiki/dist/css/edit_wiki.bundle.JOJOQRVW.css", "wiki.bundle.css": "/assets/wiki/dist/css/wiki.bundle.RIYIO5YC.css", "education.bundle.js": "/assets/education/dist/js/education.bundle.PRLG7BSX.js", "website.bundle.js": "/assets/lms/dist/js/website.bundle.DNOGFJRT.js", "lms.bundle.css": "/assets/lms/dist/css/lms.bundle.KKJYOEYT.css", "web.bundle.js": "/assets/webshop/dist/js/web.bundle.UWGVC23C.js", "webshop-web.bundle.css": "/assets/webshop/dist/css/webshop-web.bundle.DD3XS4RA.css", "healthcare.bundle.js": "/assets/healthcare/dist/js/healthcare.bundle.WR7I6ZD4.js", "hms_tz.bundle.js": "/assets/hms_tz/dist/js/hms_tz.bundle.JBXXXOHT.js", "csf_tz.bundle.js": "/assets/csf_tz/dist/js/csf_tz.bundle.I3NJFNUZ.js", "erpnext_telegram_integration.bundle.js": "/assets/erpnext_telegram_integration/dist/js/erpnext_telegram_integration.bundle.I5QFNORZ.js", "print_designer.bundle.js": "/assets/print_designer/dist/js/print_designer.bundle.KAGFGTYV.js", "pdfjs.bundle.css": "/assets/print_designer/dist/css/pdfjs.bundle.VFGAM4GJ.css", "print_designer.bundle.css": "/assets/print_designer/dist/css/print_designer.bundle.Q5Y2HU3U.css", "jobcards.bundle.js": "/assets/csf_tz/dist/js/jobcards.bundle.QKPQHFHC.js"}