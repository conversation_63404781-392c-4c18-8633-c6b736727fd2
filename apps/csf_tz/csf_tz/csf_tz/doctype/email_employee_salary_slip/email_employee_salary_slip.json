{"actions": [], "allow_rename": 1, "creation": "2022-01-03 10:33:07.861488", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["employee", "employee_name", "column_break_3", "department", "designation", "send_email"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "label": "Employee", "options": "Employee"}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_list_view": 1, "label": "Employee Name", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "in_list_view": 1, "label": "Department", "options": "Department", "read_only": 1}, {"fetch_from": "employee.designation", "fieldname": "designation", "fieldtype": "Data", "in_list_view": 1, "label": "Designation", "read_only": 1}, {"default": "0", "fieldname": "send_email", "fieldtype": "Check", "in_list_view": 1, "label": "Send Email"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2022-01-03 14:49:29.028819", "modified_by": "Administrator", "module": "CSF TZ", "name": "Email Employee <PERSON><PERSON>", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}