{"creation": "2021-04-23 23:21:59.054525", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["employee", "column_break_2", "employee_name", "section_break_4", "task", "column_break_6", "task_name", "task_rate", "section_break_9", "quantity", "column_break_10", "amount", "section_break_11", "additional_salary"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "label": "Employee", "options": "Employee"}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_list_view": 1, "label": "Employee Name", "read_only": 1}, {"fieldname": "quantity", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity"}, {"default": "0", "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "read_only": 1}, {"fieldname": "additional_salary", "fieldtype": "Link", "label": "Additional Salary", "options": "Additional Salary", "read_only": 1}, {"fieldname": "task", "fieldtype": "Link", "in_list_view": 1, "label": "Task", "options": "Piecework Type"}, {"fetch_from": "task.task_name", "fieldname": "task_name", "fieldtype": "Data", "label": "Task Name"}, {"fetch_from": "task.rate", "fieldname": "task_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Task Rate"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "section_break_11", "fieldtype": "Section Break"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}], "istable": 1, "modified": "2021-04-27 00:01:55.824888", "modified_by": "Administrator", "module": "CSF TZ", "name": "Single Piecework Employees", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}