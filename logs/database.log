2025-08-12 16:46:25,702 WARNING database DDL Query made to DB:
create table `tabSalary Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140) unique,
`salary_component_abbr` varchar(140),
`type` varchar(140),
`description` text,
`depends_on_payment_days` int(1) not null default 1,
`is_tax_applicable` int(1) not null default 1,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`is_income_tax_component` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`round_to_the_nearest_integer` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`do_not_include_in_accounts` int(1) not null default 0,
`remove_if_zero_valued` int(1) not null default 1,
`disabled` int(1) not null default 0,
`condition` longtext,
`amount` decimal(21,9) not null default 0,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`is_flexible_benefit` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`only_tax_impact` int(1) not null default 0,
`create_separate_payment_entry_against_benefit_claim` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:25,831 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:25,948 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`effective_from` date,
`company` varchar(140),
`currency` varchar(140),
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`allow_tax_exemption` int(1) not null default 0,
`amended_from` varchar(140),
`tax_relief_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:26,034 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Sub Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:26,172 WARNING database DDL Query made to DB:
create table `tabGratuity Rule Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_year` int(11) not null default 0,
`to_year` int(11) not null default 0,
`fraction_of_applicable_earnings` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:26,301 WARNING database DDL Query made to DB:
create table `tabSalary Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`letter_head` varchar(140),
`is_active` varchar(140) default 'Yes',
`is_default` varchar(140) default 'No',
`currency` varchar(140),
`amended_from` varchar(140),
`leave_encashment_amount_per_day` decimal(21,9) not null default 0,
`max_benefits` decimal(21,9) not null default 0,
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140) default 'Monthly',
`salary_component` varchar(140),
`hour_rate` decimal(21,9) not null default 0,
`total_earning` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `currency`(`currency`),
index `salary_slip_based_on_timesheet`(`salary_slip_based_on_timesheet`),
index `payroll_frequency`(`payroll_frequency`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:27,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `leave_block_list` varchar(140)
2025-08-12 16:46:27,791 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` ADD COLUMN `salary_slip` varchar(140)
2025-08-12 16:46:27,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `base_total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_billable_hours` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `base_total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_billed_hours` decimal(21,9) not null default 0
2025-08-12 16:46:27,882 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_expense_claim_payable_account` varchar(140), ADD COLUMN `default_employee_advance_account` varchar(140), ADD COLUMN `default_payroll_payable_account` varchar(140)
2025-08-12 16:46:27,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-08-12 16:46:27,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-08-12 16:46:27,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_purchase_cost` decimal(21,9) not null default 0
2025-08-12 16:46:28,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation` ADD COLUMN `appraisal_template` varchar(140)
2025-08-12 16:46:28,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-08-12 16:46:28,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `total_billing_amount` decimal(21,9) not null default 0
2025-08-12 16:46:28,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employment_type` varchar(140), ADD COLUMN `grade` varchar(140), ADD COLUMN `job_applicant` varchar(140), ADD COLUMN `default_shift` varchar(140), ADD COLUMN `expense_approver` varchar(140), ADD COLUMN `leave_approver` varchar(140), ADD COLUMN `shift_request_approver` varchar(140), ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `health_insurance_provider` varchar(140), ADD COLUMN `health_insurance_no` varchar(140)
2025-08-12 16:46:28,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-08-12 16:46:28,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` ADD COLUMN `hr` int(1) not null default 1
2025-08-12 16:46:43,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-08-12 16:46:46,281 WARNING database DDL Query made to DB:
create table `tabEmail Salary Slips` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:46,380 WARNING database DDL Query made to DB:
create table `tabRoot Cause Prevention Strategy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`solution_to_be_implemented` varchar(1000),
`consideration` varchar(140),
`specify_considerations` text,
`is_solution_implemented` varchar(140),
`estimated_cost` decimal(21,9) not null default 0,
`incidental_findings` varchar(140),
`specify_findings` text,
`date_of_completion` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:46,466 WARNING database DDL Query made to DB:
create table `tabRepack Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`default_warehouse` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:46,575 WARNING database DDL Query made to DB:
create table `tabSection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`section_name` varchar(140) unique,
`company` varchar(140),
`section_full_name` varchar(140),
`section_manager` varchar(140),
`business_license_due_date` date,
`workplace_license_expiry` date,
`cost_center` varchar(140),
`stock_adjustment` varchar(140),
`purchase_taxes_and_charges_template` varchar(140),
`default_cash_account` varchar(140),
`default_warehouse` varchar(140),
`cash_customer` varchar(140),
`monthly_target` decimal(21,9) not null default 0,
`cash_customer_pos_profile` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:46,713 WARNING database DDL Query made to DB:
create table `tabPrice Change Request Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`price_list` varchar(140),
`old_price` decimal(21,9) not null default 0,
`item_name` varchar(140),
`cost` decimal(21,9) not null default 0,
`new_price` decimal(21,9) not null default 0,
`valid_from` date,
`valid_to` date,
`price_list_currency` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:46,787 WARNING database DDL Query made to DB:
create table `tabEFD Z Report Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_number` varchar(140),
`invoice_date` date,
`amt_excl_vat` decimal(21,9) not null default 0,
`vat` decimal(21,9) not null default 0,
`amt_ex__sr` decimal(21,9) not null default 0,
`invoice_amount` decimal(21,9) not null default 0,
`include` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:46,886 WARNING database DDL Query made to DB:
create table `tabBank Statement Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`bank_account` varchar(140),
`end_of_month` date,
`bank_account_currency` varchar(140),
`count_of_transaction` int(11) not null default 0,
`deposit` decimal(21,9) not null default 0,
`withdrawal` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:47,132 WARNING database DDL Query made to DB:
create table `tabTZ Insurance Company Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`createddate` varchar(140),
`updateddate` varchar(140),
`createdby` varchar(140),
`updatedby` varchar(140),
`id` varchar(140),
`companycode` varchar(140),
`companynumber` varchar(140),
`companytypeid` varchar(140),
`companyname` varchar(140),
`businesstypeid` varchar(140),
`incorporationcertificatenumber` varchar(140),
`numberlocalstaff` varchar(140),
`numberforeignstaff` varchar(140),
`totalshareowned` varchar(140),
`totalsharealloted` varchar(140),
`incorporationdate` varchar(140),
`initialregistrationdate` varchar(140),
`businesscommencementdate` varchar(140),
`countryid` varchar(140),
`regionid` varchar(140),
`districtid` varchar(140),
`locationstreet` varchar(140),
`companyphone1` varchar(140),
`companyphone2` varchar(140),
`companyphone3` varchar(140),
`companyfax` varchar(140),
`postaladdress` varchar(140),
`emailaddress` varchar(140),
`statusid` varchar(140),
`smsnotification` varchar(140),
`emailnotification` varchar(140),
`shareholders` varchar(1000),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:47,219 WARNING database DDL Query made to DB:
create table `tabStation Members` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:47,305 WARNING database DDL Query made to DB:
create table `tabPiecework Salary Disbursement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_date` date,
`end_date` date,
`payroll_date` date,
`earning_salary_component` varchar(140),
`deduction_salary_component` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:47,395 WARNING database DDL Query made to DB:
create table `tabOTP Register` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`party_type` varchar(140),
`party` varchar(140),
`party_name` varchar(140),
`user_name` varchar(140),
`otp_type` varchar(140),
`validated` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:47,491 WARNING database DDL Query made to DB:
create table `tabDelivery Exchange Item Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_sold_or_delivered` varchar(140),
`rate_sold_or_delivered` varchar(140),
`qty_sold_or_delivered` varchar(140),
`amount_sold_or_delivered` varchar(140),
`warehouse` varchar(140),
`item_exchange` varchar(140),
`amount_exchange` varchar(140),
`uom` varchar(140),
`amended_from` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:47,603 WARNING database DDL Query made to DB:
create table `tabEFD Z Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electronic_fiscal_device` varchar(140),
`z_no` varchar(140),
`money` decimal(21,9) not null default 0,
`z_report_date_time` datetime(6),
`receipts_issued` int(11) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`total_vat` decimal(21,9) not null default 0,
`total_turnover_ex_sr` decimal(21,9) not null default 0,
`total_turnover` decimal(21,9) not null default 0,
`allowable_difference` decimal(21,9) not null default 0,
`total_excluding_vat_ticked` decimal(21,9) not null default 0,
`total_vat_ticked` decimal(21,9) not null default 0,
`total_turnover_exempted__sp_relief_ticked` decimal(21,9) not null default 0,
`total_turnover_ticked` decimal(21,9) not null default 0,
`a_turnover` decimal(21,9) not null default 0,
`b_turnover` decimal(21,9) not null default 0,
`c_turnover` decimal(21,9) not null default 0,
`d_turnover` decimal(21,9) not null default 0,
`e_turnover` decimal(21,9) not null default 0,
`a_net_sum` decimal(21,9) not null default 0,
`b_net_sum` decimal(21,9) not null default 0,
`c_net_sum` decimal(21,9) not null default 0,
`d_net_sum` decimal(21,9) not null default 0,
`a_vat` decimal(21,9) not null default 0,
`b_vat` decimal(21,9) not null default 0,
`c_vat` decimal(21,9) not null default 0,
`d_vat` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-12 16:46:47,689 WARNING database DDL Query made to DB:
create table `tabAV Report Extension` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report` varchar(140) unique,
`active` int(1) not null default 0,
`script` longtext,
`html_format` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
