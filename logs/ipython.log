2025-01-25 11:57:39,143 INFO ipython === bench console session ===
2025-01-25 11:57:39,145 INFO ipython filters = {"bl_no": "BL123"}
get_data(filters=filters)
2025-01-25 11:57:39,146 INFO ipython === session end ===
2025-02-20 17:50:11,477 INFO ipython === bench console session ===
2025-02-20 17:50:11,477 INFO ipython account = frappe.db.get_value("Account", "Debtors - ITL", ["root_type", "account_type"])
print(account)
account = frappe.db.get_value("Account", "Debtors - ITL", ["root_type", "account_type"])
print(account)
2025-02-20 17:50:11,478 INFO ipython === session end ===
2025-02-26 12:04:42,132 INFO ipython === bench console session ===
2025-02-26 12:04:42,133 INFO ipython values = frappe.db.get_values("Property Setter")
print(values)
2025-02-26 12:04:42,134 INFO ipython === session end ===
2025-04-18 15:02:33,243 INFO ipython === bench console session ===
2025-04-18 15:02:33,244 INFO ipython # List all Custom DocPerm entries for the role
frappe.get_all("Custom DocPerm", filters={"role": "Test Custom Role"})
2025-04-18 15:02:33,244 INFO ipython # In Frappe Bench Console
frappe.get_doc("DocPerm", {"parent": "Customer", "role": "Test Custom Role"})
2025-04-18 15:02:33,244 INFO ipython # In Frappe Bench Console
frappe.get_doc("Custom DocPerm", {"parent": "Customer", "role": "Test Custom Role"})
2025-04-18 15:02:33,245 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Customer", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,245 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Sales Invoices", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,246 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Sales Invoice", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,246 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Asset", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,247 INFO ipython === session end ===
2025-04-18 21:18:04,919 INFO ipython === bench console session ===
2025-04-18 21:18:04,920 INFO ipython # In Frappe Bench Console
frappe.get_all("DocPerm", {"parent": "Customer", "role": "Accounts User", "select": 1})
2025-04-18 21:18:04,921 INFO ipython # In Frappe Bench Console
frappe.get_all("DocPerm", {"parent": "Sales Invoice", "role": "Accounts User", "select": 1})
2025-04-18 21:18:04,921 INFO ipython # In Frappe Bench Console
frappe.get_all("DocPerm", {"parent": "Sales Invoice", "role": "Accounts User", "select": 0})
2025-04-18 21:18:04,922 INFO ipython frappe.get_doc("DocPerm", {"parent": "Customer", "role": "Accounts User", "read": 1})
2025-04-18 21:18:04,922 INFO ipython === session end ===
2025-05-08 00:53:08,195 INFO ipython === bench console session ===
2025-05-08 00:53:08,196 INFO ipython frappe.get_doc("Container", "ICD-C-2025-00014").__dict__
2025-05-08 00:53:08,196 INFO ipython === session end ===
2025-06-05 17:49:56,904 INFO ipython === bench console session ===
2025-06-05 17:49:56,905 INFO ipython import frappe
2025-06-05 17:49:56,905 INFO ipython frappe.get_hooks("doc_events")
2025-06-05 17:49:56,905 INFO ipython from icd_tz.icd_tz.api.payment_entry import clear_paid_invoice_references
2025-06-05 17:49:56,905 INFO ipython print("Import successful!")
2025-06-05 17:49:56,905 INFO ipython === session end ===
2025-06-05 17:53:42,837 INFO ipython === bench console session ===
2025-06-05 17:53:42,838 INFO ipython from icd_tz.icd_tz.api.payment_entry import clear_invoice_references_from_charges
2025-06-05 17:53:42,838 INFO ipython print("Import successful!")
2025-06-05 17:53:42,838 INFO ipython settings = frappe.get_doc("ICD TZ Settings")
2025-06-05 17:53:42,839 INFO ipython print(f"Found {len(settings.service_types)} service types")
2025-06-05 17:53:42,839 INFO ipython for row in settings.service_types[:3]:
        print(f"Service: {row.service_name}, Type: {row.service_type}")
        
2025-06-05 17:53:42,839 INFO ipython === session end ===
2025-06-05 18:01:53,309 INFO ipython === bench console session ===
2025-06-05 18:01:53,311 INFO ipython # Let's check if there are any Sales Invoices with container_id populated
2025-06-05 18:01:53,312 INFO ipython sales_invoices = frappe.db.sql("""
    SELECT si.name, si.m_bl_no, sii.container_id, sii.item_code
        FROM `tabSales Invoice` si
            JOIN `tabSales Invoice Item` sii ON si.name = sii.parent
                WHERE si.m_bl_no IS NOT NULL AND si.m_bl_no != ''
                    LIMIT 5
                    """, as_dict=True)
2025-06-05 18:01:53,312 INFO ipython for invoice in sales_invoices:
        print(f"Invoice: {invoice.name}, M_BL: {invoice.m_bl_no}, Container ID: {invoice.container_id}, Item: {invoice.item_code}")
        
2025-06-05 18:01:53,312 INFO ipython # Let's check a specific container to see if invoice references are set
2025-06-05 18:01:53,312 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 18:01:53,312 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 18:01:53,313 INFO ipython print(f"Container: {container.name}")
2025-06-05 18:01:53,313 INFO ipython print(f"Removal charges: {container.has_removal_charges}, Invoice: {container.r_sales_invoice}")
2025-06-05 18:01:53,313 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 18:01:53,313 INFO ipython print(f"Days to be billed: {container.days_to_be_billed}")
2025-06-05 18:01:53,313 INFO ipython # Check container reception
2025-06-05 18:01:53,313 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:01:53,313 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:01:53,314 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:01:53,314 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:01:53,314 INFO ipython else:
        print("No container reception found")
2025-06-05 18:01:53,314 INFO ipython === session end ===
2025-06-05 18:07:47,415 INFO ipython === bench console session ===
2025-06-05 18:07:47,415 INFO ipython # Let's test the sales invoice hook manually
2025-06-05 18:07:47,415 INFO ipython invoice_name = "ACC-SINV-2025-01669"
2025-06-05 18:07:47,416 INFO ipython from icd_tz.icd_tz.api.sales_invoice import update_sales_references
2025-06-05 18:07:47,416 INFO ipython # Get the invoice
2025-06-05 18:07:47,416 INFO ipython invoice_doc = frappe.get_doc("Sales Invoice", invoice_name)
2025-06-05 18:07:47,416 INFO ipython print(f"Invoice: {invoice_doc.name}, M_BL: {invoice_doc.m_bl_no}")
2025-06-05 18:07:47,416 INFO ipython print(f"Items count: {len(invoice_doc.items)}")
2025-06-05 18:07:47,416 INFO ipython # Test the update function
2025-06-05 18:07:47,416 INFO ipython update_sales_references(invoice_doc)
2025-06-05 18:07:47,417 INFO ipython # Check if the container references were updated
2025-06-05 18:07:47,417 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 18:07:47,417 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 18:07:47,417 INFO ipython print(f"After update:")
2025-06-05 18:07:47,417 INFO ipython print(f"Removal charges: {container.has_removal_charges}, Invoice: {container.r_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython # Check container reception
2025-06-05 18:07:47,417 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython else:
        print("No container reception found")
2025-06-05 18:07:47,418 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython else:
        print("No container reception found")
2025-06-05 18:07:47,418 INFO ipython reception = frappe.get_doc("Container Reception", container.container_reception)
2025-06-05 18:07:47,418 INFO ipython print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython # Let's check what the current status should be for Gate Pass validation
2025-06-05 18:07:47,418 INFO ipython print("=== Current Container Status ===")
2025-06-05 18:07:47,419 INFO ipython print(f"Container: {container.name}")
2025-06-05 18:07:47,419 INFO ipython print(f"Days to be billed: {container.days_to_be_billed}")
2025-06-05 18:07:47,419 INFO ipython print(f"Has removal charges: {container.has_removal_charges}")
2025-06-05 18:07:47,419 INFO ipython print(f"Has corridor charges: {container.has_corridor_levy_charges}")
2025-06-05 18:07:47,419 INFO ipython print(f"Removal invoice: {container.r_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print(f"Corridor invoice: {container.c_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print("\n=== Gate Pass Validation Logic ===")
2025-06-05 18:07:47,420 INFO ipython print("1. Storage charges pending:", container.days_to_be_billed > 0)
2025-06-05 18:07:47,420 INFO ipython print("2. Removal charges pending:", container.has_removal_charges == "Yes" and not container.r_sales_invoice)
2025-06-05 18:07:47,420 INFO ipython print("3. Corridor charges pending:", container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice)
2025-06-05 18:07:47,420 INFO ipython # Check the invoice payment status
2025-06-05 18:07:47,420 INFO ipython print(f"Invoice outstanding amount: {invoice_doc.outstanding_amount}")
2025-06-05 18:07:47,420 INFO ipython print(f"Invoice status: {invoice_doc.status}")
2025-06-05 18:07:47,420 INFO ipython print(f"Invoice grand total: {invoice_doc.grand_total}")
2025-06-05 18:07:47,420 INFO ipython # This container should NOT have pending payments according to the validation logic
2025-06-05 18:07:47,420 INFO ipython print("\n=== CONCLUSION ===")
2025-06-05 18:07:47,421 INFO ipython print("All validation checks are FALSE, so there should be NO pending payments error!")
2025-06-05 18:07:47,421 INFO ipython === session end ===
2025-06-05 18:18:23,384 INFO ipython === bench console session ===
2025-06-05 18:18:23,386 INFO ipython # Test the clear function on the cancelled invoice
2025-06-05 18:18:23,386 INFO ipython invoice_name = "ACC-SINV-2025-01669"
2025-06-05 18:18:23,386 INFO ipython from icd_tz.icd_tz.api.sales_invoice import clear_sales_references
2025-06-05 18:18:23,386 INFO ipython # Get the cancelled invoice
2025-06-05 18:18:23,386 INFO ipython invoice_doc = frappe.get_doc("Sales Invoice", invoice_name)
2025-06-05 18:18:23,386 INFO ipython print(f"Invoice: {invoice_doc.name}, Status: {invoice_doc.status}")
2025-06-05 18:18:23,387 INFO ipython # Clear the references
2025-06-05 18:18:23,387 INFO ipython clear_sales_references(invoice_doc)
2025-06-05 18:18:23,387 INFO ipython print("References cleared!")
2025-06-05 18:18:23,387 INFO ipython # Check if the container references were cleared
2025-06-05 18:18:23,387 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 18:18:23,387 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 18:18:23,387 INFO ipython print(f"=== After clearing references ===")
2025-06-05 18:18:23,387 INFO ipython print(f"Removal charges: {container.has_removal_charges}, Invoice: {container.r_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython # Check container reception
2025-06-05 18:18:23,388 INFO ipython reception = frappe.get_doc("Container Reception", container.container_reception)
2025-06-05 18:18:23,388 INFO ipython print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython print("\n=== Gate Pass Validation Logic ===")
2025-06-05 18:18:23,388 INFO ipython print("1. Storage charges pending:", container.days_to_be_billed > 0)
2025-06-05 18:18:23,388 INFO ipython print("2. Removal charges pending:", container.has_removal_charges == "Yes" and not container.r_sales_invoice)
2025-06-05 18:18:23,389 INFO ipython print("3. Corridor charges pending:", container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice)
2025-06-05 18:18:23,389 INFO ipython === session end ===
2025-06-05 22:14:29,805 INFO ipython === bench console session ===
2025-06-05 22:14:29,806 INFO ipython # Let's investigate the real issue - check for ACTIVE invoices with outstanding amounts
2025-06-05 22:14:29,806 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 22:14:29,806 INFO ipython # Check for submitted invoices (not cancelled) with this container
2025-06-05 22:14:29,806 INFO ipython active_invoices = frappe.db.sql("""
    SELECT si.name, si.status, si.outstanding_amount, si.grand_total, sii.item_code
        FROM `tabSales Invoice` si
            JOIN `tabSales Invoice Item` sii ON si.name = sii.parent
                WHERE sii.container_id = %s 
                    AND si.docstatus = 1 
                        AND si.status != 'Cancelled'
                            ORDER BY si.creation DESC
                            """, (container_id,), as_dict=True)
2025-06-05 22:14:29,806 INFO ipython print(f"=== ACTIVE INVOICES for Container {container_id} ===")
2025-06-05 22:14:29,807 INFO ipython if not active_invoices:
        print("No active invoices found!")
        else:
                for invoice in active_invoices:
                            print(f"Invoice: {invoice.name}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Status: {invoice.status}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Outstanding: {invoice.outstanding_amount}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Grand Total: {invoice.grand_total}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Item: {invoice.item_code}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Paid: {'Yes' if invoice.outstanding_amount == 0 else 'No'}")
2025-06-05 22:14:29,808 INFO ipython         print("---")
2025-06-05 22:14:29,808 INFO ipython print(f"Number of active invoices: {len(active_invoices)}")
2025-06-05 22:14:29,808 INFO ipython for i, invoice in enumerate(active_invoices):
        print(f"{i+1}. Invoice: {invoice.name}, Status: {invoice.status}, Outstanding: {invoice.outstanding_amount}, Item: {invoice.item_code}")
        
2025-06-05 22:14:29,808 INFO ipython # Check current container references
2025-06-05 22:14:29,808 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 22:14:29,808 INFO ipython reception = frappe.get_doc("Container Reception", container.container_reception)
2025-06-05 22:14:29,808 INFO ipython print("=== CURRENT CONTAINER REFERENCES ===")
2025-06-05 22:14:29,808 INFO ipython print(f"Container corridor invoice: {container.c_sales_invoice}")
2025-06-05 22:14:29,808 INFO ipython print(f"Container removal invoice: {container.r_sales_invoice}")
2025-06-05 22:14:29,809 INFO ipython print(f"Reception transport invoice: {reception.t_sales_invoice}")
2025-06-05 22:14:29,809 INFO ipython print(f"Reception shore invoice: {reception.s_sales_invoice}")
2025-06-05 22:14:29,809 INFO ipython print("\n=== SHOULD BE POINTING TO ===")
2025-06-05 22:14:29,809 INFO ipython print(f"New PAID invoice: ACC-SINV-2025-01670")
2025-06-05 22:14:29,809 INFO ipython print("\n=== THE SOLUTION ===")
2025-06-05 22:14:29,809 INFO ipython print("The invoice references need to be updated to point to the new PAID invoice!")
2025-06-05 22:14:29,809 INFO ipython print("This is why the Gate Pass validation fails - it's checking against the old cancelled invoice.")
2025-06-05 22:14:29,809 INFO ipython # Fix the missing corridor invoice reference
2025-06-05 22:14:29,809 INFO ipython print("=== FIXING THE ISSUE ===")
2025-06-05 22:14:29,809 INFO ipython print(f"Setting corridor invoice to: ACC-SINV-2025-01670")
2025-06-05 22:14:29,809 INFO ipython # Update the corridor invoice reference
2025-06-05 22:14:29,809 INFO ipython container.c_sales_invoice = "ACC-SINV-2025-01670"
2025-06-05 22:14:29,810 INFO ipython container.save(ignore_permissions=True)
2025-06-05 22:14:29,810 INFO ipython print("Container updated!")
2025-06-05 22:14:29,810 INFO ipython # Verify the fix
2025-06-05 22:14:29,810 INFO ipython container.reload()
2025-06-05 22:14:29,810 INFO ipython print(f"\nAfter fix:")
2025-06-05 22:14:29,810 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 22:14:29,810 INFO ipython print("\n=== Final Gate Pass Validation ===")
2025-06-05 22:14:29,810 INFO ipython print("1. Storage charges pending:", container.days_to_be_billed > 0)
2025-06-05 22:14:29,810 INFO ipython print("2. Removal charges pending:", container.has_removal_charges == "Yes" and not container.r_sales_invoice)
2025-06-05 22:14:29,811 INFO ipython print("3. Corridor charges pending:", container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice)
2025-06-05 22:14:29,811 INFO ipython any_pending = (container.days_to_be_billed > 0 or 
               (container.has_removal_charges == "Yes" and not container.r_sales_invoice) or
                              (container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice))
2025-06-05 22:14:29,811 INFO ipython print(f"\nAny pending payments: {any_pending}")
2025-06-05 22:14:29,811 INFO ipython print("🎉 Gate Pass should be allowed!" if not any_pending else "❌ Gate Pass will be blocked!")
2025-06-05 22:14:29,811 INFO ipython === session end ===
2025-06-19 17:42:19,491 INFO ipython === bench console session ===
2025-06-19 17:42:19,493 INFO ipython import frappe
2025-06-19 17:42:19,493 INFO ipython # Create a test employee
2025-06-19 17:42:19,493 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee for Deletion",
            "first_name": "Test",
                "last_name": "Employee",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active"
                                })
2025-06-19 17:42:19,493 INFO ipython test_employee.insert()
2025-06-19 17:42:19,494 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:42:19,494 INFO ipython # Test deletion immediately
2025-06-19 17:42:19,494 INFO ipython try:
        frappe.delete_doc("Employee", test_employee.name)
            print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:42:19,494 INFO ipython except Exception as e:
        print(f"❌ ERROR: Failed to delete employee: {e}")
2025-06-19 17:42:19,494 INFO ipython frappe.db.commit()
2025-06-19 17:42:19,494 INFO ipython # Get a department first
2025-06-19 17:42:19,494 INFO ipython departments = frappe.get_all("Department", limit=1)
2025-06-19 17:42:19,495 INFO ipython if departments:
        dept = departments[0].name
        else:
                # Create a test department
2025-06-19 17:42:19,495 INFO ipython     test_dept = frappe.get_doc({
        "doctype": "Department",
            "department_name": "Test Department"
            })
2025-06-19 17:42:19,495 INFO ipython     test_dept.insert()
2025-06-19 17:42:19,495 INFO ipython     dept = test_dept.name
2025-06-19 17:42:19,495 INFO ipython print(f"Using department: {dept}")
2025-06-19 17:42:19,495 INFO ipython # Create a test employee with all required fields
2025-06-19 17:42:19,495 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee for Deletion",
            "first_name": "Test",
                "last_name": "Employee", 
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": dept,
                                        "national_identity": "123456789"
                                        })
2025-06-19 17:42:19,495 INFO ipython test_employee.insert()
2025-06-19 17:42:19,496 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:42:19,496 INFO ipython # Test deletion
2025-06-19 17:42:19,496 INFO ipython try:
        frappe.delete_doc("Employee", test_employee.name)
            print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:42:19,496 INFO ipython except Exception as e:
        print(f"❌ ERROR: Failed to delete employee: {e}")
2025-06-19 17:42:19,497 INFO ipython frappe.db.commit()
2025-06-19 17:42:19,497 INFO ipython frappe.delete_doc("Employee", "HR-EMP-00028")
2025-06-19 17:42:19,497 INFO ipython print("✅ SUCCESS: Employee HR-EMP-00028 deleted successfully!")
2025-06-19 17:42:19,497 INFO ipython === session end ===
2025-06-19 17:50:25,977 INFO ipython === bench console session ===
2025-06-19 17:50:25,978 INFO ipython import frappe
2025-06-19 17:50:25,978 INFO ipython # Create a test employee
2025-06-19 17:50:25,978 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee Final",
            "first_name": "Test",
                "last_name": "Final",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "987654321"
                                        })
2025-06-19 17:50:25,978 INFO ipython test_employee.insert()
2025-06-19 17:50:25,978 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:50:25,978 INFO ipython # Test deletion
2025-06-19 17:50:25,979 INFO ipython frappe.delete_doc("Employee", test_employee.name)
2025-06-19 17:50:25,979 INFO ipython print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:50:25,979 INFO ipython frappe.db.commit()
2025-06-19 17:50:25,979 INFO ipython # Test force deletion (bypasses link checking)
2025-06-19 17:50:25,979 INFO ipython test_employee2 = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee Force",
            "first_name": "Test",
                "last_name": "Force",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "111222333"
                                        })
2025-06-19 17:50:25,979 INFO ipython test_employee2.insert()
2025-06-19 17:50:25,979 INFO ipython print(f"Created test employee: {test_employee2.name} - {test_employee2.employee_name}")
2025-06-19 17:50:25,979 INFO ipython # Force delete (bypasses link checking)
2025-06-19 17:50:25,979 INFO ipython frappe.delete_doc("Employee", test_employee2.name, force=True)
2025-06-19 17:50:25,980 INFO ipython print(f"✅ SUCCESS: Employee {test_employee2.name} force deleted successfully!")
2025-06-19 17:50:25,980 INFO ipython frappe.db.commit()
2025-06-19 17:50:25,980 INFO ipython === session end ===
2025-06-19 18:02:39,571 INFO ipython === bench console session ===
2025-06-19 18:02:39,572 INFO ipython import frappe
2025-06-19 18:02:39,572 INFO ipython # Create a final test employee
2025-06-19 18:02:39,572 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Final Test Employee",
            "first_name": "Final",
                "last_name": "Test",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "999888777"
                                        })
2025-06-19 18:02:39,572 INFO ipython test_employee.insert()
2025-06-19 18:02:39,573 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 18:02:39,573 INFO ipython # Test normal deletion (should work without errors now)
2025-06-19 18:02:39,573 INFO ipython frappe.delete_doc("Employee", test_employee.name)
2025-06-19 18:02:39,573 INFO ipython print(f"🎉 SUCCESS: Employee {test_employee.name} deleted successfully WITHOUT ERRORS!")
2025-06-19 18:02:39,573 INFO ipython frappe.db.commit()
2025-06-19 18:02:39,573 INFO ipython === session end ===
2025-07-03 15:00:40,818 INFO ipython === bench console session ===
2025-07-03 15:00:40,820 INFO ipython # First, let's check if there are any employees
2025-07-03 15:00:40,820 INFO ipython employees = frappe.get_all("Employee", fields=["name", "employee_name"], limit=5)
2025-07-03 15:00:40,820 INFO ipython print("Available employees:")
2025-07-03 15:00:40,820 INFO ipython for emp in employees:
        print(f"- {emp.name}: {emp.employee_name}")
        
2025-07-03 15:00:40,820 INFO ipython # Let's test the current code with ignore_permissions=True
2025-07-03 15:00:40,820 INFO ipython # First, let's create a test Employee Advance
2025-07-03 15:00:40,821 INFO ipython test_employee = "HR-EMP-00005"  # Issa Selemani Msongola
2025-07-03 15:00:40,821 INFO ipython # Create a test Employee Advance
2025-07-03 15:00:40,821 INFO ipython advance = frappe.new_doc("Employee Advance")
2025-07-03 15:00:40,821 INFO ipython advance.employee = test_employee
2025-07-03 15:00:40,821 INFO ipython advance.purpose = "Test Travel Advance"
2025-07-03 15:00:40,821 INFO ipython advance.advance_amount = 50000
2025-07-03 15:00:40,821 INFO ipython advance.travel_request_ref = "TEST-TR-001"  # This is required for the hook to trigger
2025-07-03 15:00:40,821 INFO ipython advance.company = "Clouds Entertainment Co Ltd"
2025-07-03 15:00:40,821 INFO ipython advance.advance_account = "Employee Advances - CE"
2025-07-03 15:00:40,821 INFO ipython advance.insert()
2025-07-03 15:00:40,821 INFO ipython print(f"Created Employee Advance: {advance.name}")
2025-07-03 15:00:40,822 INFO ipython # Let's check what Travel Requests exist
2025-07-03 15:00:40,822 INFO ipython travel_requests = frappe.get_all("Travel Request", fields=["name", "purpose"], limit=5)
2025-07-03 15:00:40,822 INFO ipython print("Available Travel Requests:")
2025-07-03 15:00:40,822 INFO ipython for tr in travel_requests:
        print(f"- {tr.name}: {tr.purpose}")
        # Let's check what Travel Requests exist with basic fields
        travel_requests = frappe.get_all("Travel Request", fields=["name"], limit=5)
        print("Available Travel Requests:")
        for tr in travel_requests:
                print(f"- {tr.name}")
                
2025-07-03 15:00:40,822 INFO ipython # If there are any, let's use the first one
2025-07-03 15:00:40,822 INFO ipython if travel_requests:
        valid_tr = travel_requests[0].name
            print(f"Using Travel Request: {valid_tr}")
2025-07-03 15:00:40,822 INFO ipython else:
        print("No Travel Requests found")
2025-07-03 15:00:40,822 INFO ipython # Let's test the function directly without creating Employee Advance
2025-07-03 15:00:40,822 INFO ipython # First, let's import the function
2025-07-03 15:00:40,822 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-03 15:00:40,822 INFO ipython # Let's check if there are any existing Employee Advances with travel_request_ref
2025-07-03 15:00:40,822 INFO ipython existing_advances = frappe.get_all("Employee Advance", 
                                 filters={"travel_request_ref": ["!=", ""]}, 
                                                                  fields=["name", "travel_request_ref"], 
                                                                                                   limit=3)
2025-07-03 15:00:40,823 INFO ipython print("Existing Employee Advances with travel_request_ref:")
2025-07-03 15:00:40,823 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site rubis console
2025-07-03 15:00:40,823 INFO ipython === session end ===
2025-07-06 17:58:13,327 INFO ipython === bench console session ===
2025-07-06 17:58:13,328 INFO ipython === session end ===
2025-07-06 18:00:45,241 INFO ipython === bench console session ===
2025-07-06 18:00:45,241 INFO ipython === session end ===
2025-07-06 18:01:02,543 INFO ipython === bench console session ===
2025-07-06 18:01:02,544 INFO ipython import frappe
2025-07-06 18:01:02,544 INFO ipython # Check if the patch has already been executed
2025-07-06 18:01:02,544 INFO ipython patches = frappe.db.sql("SELECT patch FROM `tabPatch Log` WHERE patch='frappe.patches.v16_0.enable_setup_complete'", as_dict=True)
2025-07-06 18:01:02,544 INFO ipython print("Patch already executed:", len(patches) > 0)
2025-07-06 18:01:02,544 INFO ipython # Manually mark the patch as completed if it's stuck
2025-07-06 18:01:02,544 INFO ipython if len(patches) == 0:
    frappe.db.sql("INSERT INTO `tabPatch Log` (patch) VALUES ('frappe.patches.v16_0.enable_setup_complete')")
    frappe.db.commit()
    print("Patch marked as completed")
else:
    print("Patch was already in the log")
    
2025-07-06 18:01:02,544 INFO ipython === session end ===
2025-07-06 18:01:16,343 INFO ipython === bench console session ===
2025-07-06 18:01:16,343 INFO ipython import frappe
2025-07-06 18:01:16,344 INFO ipython from frappe.utils import now_datetime
2025-07-06 18:01:16,344 INFO ipython # Check if the patch has already been executed
2025-07-06 18:01:16,344 INFO ipython patches = frappe.db.sql("SELECT patch FROM `tabPatch Log` WHERE patch='frappe.patches.v16_0.enable_setup_complete'", as_dict=True)
2025-07-06 18:01:16,344 INFO ipython print("Patch already executed:", len(patches) > 0)
2025-07-06 18:01:16,344 INFO ipython # Manually mark the patch as completed if it's stuck
2025-07-06 18:01:16,344 INFO ipython if len(patches) == 0:
    patch_name = frappe.generate_hash(length=10)
    frappe.db.sql("""
        INSERT INTO `tabPatch Log` (name, owner, creation, modified, modified_by, patch) 
        VALUES (%s, 'Administrator', %s, %s, 'Administrator', 'frappe.patches.v16_0.enable_setup_complete')
    """, (patch_name, now_datetime(), now_datetime()))
    frappe.db.commit()
    print("Patch marked as completed")
else:
    print("Patch was already in the log")
2025-07-06 18:01:16,344 INFO ipython # Also ensure setup_complete is set
2025-07-06 18:01:16,344 INFO ipython frappe.db.set_single_value('System Settings', 'setup_complete', 1)
2025-07-06 18:01:16,345 INFO ipython frappe.db.commit()
2025-07-06 18:01:16,345 INFO ipython print("Setup complete flag set")
2025-07-06 18:01:16,345 INFO ipython === session end ===
2025-07-06 18:05:08,694 INFO ipython === bench console session ===
2025-07-06 18:05:08,694 INFO ipython import frappe
2025-07-06 18:05:08,694 INFO ipython # Check which doctype is causing the issue
2025-07-06 18:05:08,695 INFO ipython print("Checking installed apps...")
2025-07-06 18:05:08,695 INFO ipython apps = frappe.get_installed_apps()
2025-07-06 18:05:08,696 INFO ipython print("Installed apps:", apps)
2025-07-06 18:05:08,696 INFO ipython # Try to identify which doctype might be stuck
2025-07-06 18:05:08,696 INFO ipython frappe.db.sql("SHOW PROCESSLIST")
2025-07-06 18:05:08,697 INFO ipython === session end ===
2025-07-06 18:06:29,755 INFO ipython === bench console session ===
2025-07-06 18:06:29,755 INFO ipython import frappe
2025-07-06 18:06:29,755 INFO ipython from frappe.model.sync import sync_for
2025-07-06 18:06:29,755 INFO ipython # Try to sync each app individually to see which one is causing issues
2025-07-06 18:06:29,756 INFO ipython apps = frappe.get_installed_apps()
2025-07-06 18:06:29,756 INFO ipython for app in apps:
    try:
        print(f"Syncing {app}...")
        sync_for(app, force=0, reset_permissions=False)
        print(f"{app} synced successfully")
    except Exception as e:
        print(f"Error syncing {app}: {str(e)[:200]}")
        break
2025-07-06 18:06:29,756 INFO ipython === session end ===
2025-07-07 22:36:17,928 INFO ipython === bench console session ===
2025-07-07 22:36:17,928 INFO ipython frappe.db.exists("DocType", "Bank Reconciliation Filter")
2025-07-07 22:36:17,928 INFO ipython === session end ===
2025-07-07 22:40:16,726 INFO ipython === bench console session ===
2025-07-07 22:40:16,726 INFO ipython from csf_tz.csftz_hooks.enhanced_bank_reconciliation import EnhancedMatchingEngine
2025-07-07 22:40:16,727 INFO ipython print("Enhanced Bank Reconciliation module loaded successfully!")
2025-07-07 22:40:16,727 INFO ipython === session end ===
2025-07-07 22:50:19,099 INFO ipython === bench console session ===
2025-07-07 22:50:19,100 INFO ipython from csf_tz.csftz_hooks.enhanced_bank_reconciliation import get_enhanced_linked_payments
2025-07-07 22:50:19,100 INFO ipython print("Enhanced Bank Reconciliation module is working!")
2025-07-07 22:50:19,100 INFO ipython === session end ===
2025-07-08 15:19:27,442 INFO ipython === bench console session ===
2025-07-08 15:19:27,445 INFO ipython print('🔍 Testing Employee Advance Payment Hook...')
2025-07-08 15:19:27,445 INFO ipython print('=' * 60)
2025-07-08 15:19:27,445 INFO ipython # Check if the hook file exists and can be imported
2025-07-08 15:19:27,446 INFO ipython try:
    from csf_tz.csftz_hooks.employee_advance_payment_and_expense import execute, create_payment_entry
    print('✅ Hook file imported successfully')
except ImportError as e:
    print(f'❌ Failed to import hook: {e}')
2025-07-08 15:19:27,446 INFO ipython # Check for active employees
2025-07-08 15:19:27,446 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=3)
2025-07-08 15:19:27,446 INFO ipython print(f'Found {len(employees)} active employees')
2025-07-08 15:19:27,446 INFO ipython # Check for companies  
2025-07-08 15:19:27,446 INFO ipython companies = frappe.get_all('Company', limit=3)
2025-07-08 15:19:27,446 INFO ipython print(f'Found {len(companies)} companies')
2025-07-08 15:19:27,447 INFO ipython print('✅ Basic checks complete!')
2025-07-08 15:19:27,447 INFO ipython === session end ===
2025-07-08 15:20:03,089 INFO ipython === bench console session ===
2025-07-08 15:20:03,089 INFO ipython print('🧪 Testing Payment Entry Creation...')
2025-07-08 15:20:03,090 INFO ipython print('=' * 60)
2025-07-08 15:20:03,090 INFO ipython # Get an active employee
2025-07-08 15:20:03,090 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:20:03,090 INFO ipython if not employees:
    print('❌ No active employees found!')
    exit()
2025-07-08 15:20:03,090 INFO ipython test_employee = employees[0].name
2025-07-08 15:20:03,090 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:20:03,090 INFO ipython # Get a company
2025-07-08 15:20:03,090 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:20:03,090 INFO ipython if not companies:
    print('❌ No companies found!')
    exit()
2025-07-08 15:20:03,090 INFO ipython test_company = companies[0].name
2025-07-08 15:20:03,090 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:20:03,090 INFO ipython # Test the create_payment_entry function directly
2025-07-08 15:20:03,090 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:20:03,091 INFO ipython # Create a mock Employee Advance document
2025-07-08 15:20:03,091 INFO ipython class MockDoc:
    def __init__(self):
        self.name = 'TEST-ADV-001'
        self.employee = test_employee
        self.company = test_company
        self.advance_amount = 1000
        self.travel_request_ref = 'TEST-TR-001'
        self.docstatus = 1
        self.advance_account = None
        self.mode_of_payment = None
2025-07-08 15:20:03,091 INFO ipython mock_doc = MockDoc()
2025-07-08 15:20:03,091 INFO ipython print(f'Testing payment entry creation for {mock_doc.name}...')
2025-07-08 15:20:03,091 INFO ipython try:
    result = create_payment_entry(mock_doc)
    if result:
        print(f'✅ SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Amount: {result.paid_amount}')
        print(f'   - Status: {result.docstatus}')
    else:
        print('❌ Function returned None')
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
    import traceback
    traceback.print_exc()
2025-07-08 15:20:03,091 INFO ipython print('✅ Test complete!')
2025-07-08 15:20:03,091 INFO ipython === session end ===
2025-07-08 15:20:34,407 INFO ipython === bench console session ===
2025-07-08 15:20:34,407 INFO ipython #!/usr/bin/env python3
2025-07-08 15:20:34,407 INFO ipython print('🧪 Testing Payment Entry Creation...')
2025-07-08 15:20:34,407 INFO ipython print('=' * 60)
2025-07-08 15:20:34,408 INFO ipython # Get an active employee
2025-07-08 15:20:34,408 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:20:34,408 INFO ipython if not employees:
    print('❌ No active employees found!')
    exit()
2025-07-08 15:20:34,408 INFO ipython test_employee = employees[0].name
2025-07-08 15:20:34,408 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:20:34,408 INFO ipython # Get a company
2025-07-08 15:20:34,408 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:20:34,408 INFO ipython if not companies:
    print('❌ No companies found!')
    exit()
2025-07-08 15:20:34,408 INFO ipython test_company = companies[0].name
2025-07-08 15:20:34,408 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:20:34,408 INFO ipython # Test the create_payment_entry function directly
2025-07-08 15:20:34,408 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:20:34,408 INFO ipython # Create a mock Employee Advance document
2025-07-08 15:20:34,408 INFO ipython class MockDoc:
    def __init__(self, employee, company):
        self.name = 'TEST-ADV-001'
        self.employee = employee
        self.company = company
        self.advance_amount = 1000
        self.travel_request_ref = 'TEST-TR-001'
        self.docstatus = 1
        self.advance_account = None
        self.mode_of_payment = None
2025-07-08 15:20:34,409 INFO ipython mock_doc = MockDoc(test_employee, test_company)
2025-07-08 15:20:34,409 INFO ipython print(f'Testing payment entry creation for {mock_doc.name}...')
2025-07-08 15:20:34,409 INFO ipython try:
    result = create_payment_entry(mock_doc)
    if result:
        print(f'✅ SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Amount: {result.paid_amount}')
        print(f'   - Status: {result.docstatus}')
    else:
        print('❌ Function returned None')
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
    import traceback
    traceback.print_exc()
2025-07-08 15:20:34,409 INFO ipython print('✅ Test complete!')
2025-07-08 15:20:34,409 INFO ipython === session end ===
2025-07-08 15:21:00,990 INFO ipython === bench console session ===
2025-07-08 15:21:00,990 INFO ipython #!/usr/bin/env python3
2025-07-08 15:21:00,990 INFO ipython print('🧪 Testing with Real Employee Advance...')
2025-07-08 15:21:00,990 INFO ipython print('=' * 60)
2025-07-08 15:21:00,990 INFO ipython # Get an active employee
2025-07-08 15:21:00,990 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:21:00,990 INFO ipython if not employees:
    print('❌ No active employees found!')
    exit()
2025-07-08 15:21:00,990 INFO ipython test_employee = employees[0].name
2025-07-08 15:21:00,991 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:21:00,991 INFO ipython # Get a company
2025-07-08 15:21:00,991 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:21:00,991 INFO ipython if not companies:
    print('❌ No companies found!')
    exit()
2025-07-08 15:21:00,991 INFO ipython test_company = companies[0].name
2025-07-08 15:21:00,991 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:21:00,991 INFO ipython # Create a real Employee Advance document
2025-07-08 15:21:00,991 INFO ipython print('Creating a real Employee Advance...')
2025-07-08 15:21:00,991 INFO ipython try:
    advance = frappe.new_doc("Employee Advance")
    advance.employee = test_employee
    advance.company = test_company
    advance.purpose = "Test Travel Advance for Payment Entry"
    advance.advance_amount = 1000
    advance.posting_date = frappe.utils.nowdate()
    advance.travel_request_ref = "TEST-TR-001"  # This triggers the hook
    
    # Insert the advance first
    advance.insert()
    print(f'✅ Employee Advance {advance.name} created successfully')
    
    # Now test our payment entry creation function
    from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
    
    print(f'Testing payment entry creation for {advance.name}...')
    
    result = create_payment_entry(advance)
    if result:
        print(f'✅ SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Amount: {result.paid_amount}')
        print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
    else:
        print('❌ Function returned None')
        
    # Clean up - delete the test advance
    advance.delete()
    print(f'🗑️  Cleaned up test advance {advance.name}')
    
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
    import traceback
    traceback.print_exc()
2025-07-08 15:21:00,991 INFO ipython print('✅ Test complete!')
2025-07-08 15:21:00,991 INFO ipython === session end ===
2025-07-08 15:21:30,924 INFO ipython === bench console session ===
2025-07-08 15:21:30,924 INFO ipython #!/usr/bin/env python3
2025-07-08 15:21:30,924 INFO ipython print('🧪 Testing with Real Travel Request...')
2025-07-08 15:21:30,924 INFO ipython print('=' * 60)
2025-07-08 15:21:30,924 INFO ipython # Get an active employee
2025-07-08 15:21:30,924 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:21:30,925 INFO ipython if not employees:
    print('❌ No active employees found!')
    exit()
2025-07-08 15:21:30,925 INFO ipython test_employee = employees[0].name
2025-07-08 15:21:30,925 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:21:30,925 INFO ipython # Get a company
2025-07-08 15:21:30,925 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:21:30,925 INFO ipython if not companies:
    print('❌ No companies found!')
    exit()
2025-07-08 15:21:30,925 INFO ipython test_company = companies[0].name
2025-07-08 15:21:30,925 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:21:30,925 INFO ipython # Check for existing Travel Requests
2025-07-08 15:21:30,925 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=5, fields=['name', 'employee'])
2025-07-08 15:21:30,926 INFO ipython print(f'Found {len(travel_requests)} Travel Requests')
2025-07-08 15:21:30,926 INFO ipython if travel_requests:
    for tr in travel_requests:
        print(f'   - {tr.name} (Employee: {tr.employee})')
    
2025-07-08 15:21:30,926 INFO ipython     # Use the first travel request
2025-07-08 15:21:30,926 INFO ipython     test_travel_request = travel_requests[0].name
2025-07-08 15:21:30,926 INFO ipython     print(f'Using Travel Request: {test_travel_request}')
2025-07-08 15:21:30,926 INFO ipython else:
    print('⚠️  No Travel Requests found. Creating one...')
2025-07-08 15:21:30,926 INFO ipython     # Create a Travel Request first
2025-07-08 15:21:30,926 INFO ipython     try:
        tr = frappe.new_doc("Travel Request")
        tr.employee = test_employee
        tr.company = test_company
        tr.purpose = "Test Travel for Payment Entry Testing"
        tr.from_date = frappe.utils.nowdate()
        tr.to_date = frappe.utils.add_days(frappe.utils.nowdate(), 3)
        tr.insert()
        test_travel_request = tr.name
        print(f'✅ Created Travel Request: {test_travel_request}')
    except Exception as e:
        print(f'❌ Failed to create Travel Request: {e}')
        test_travel_request = None
2025-07-08 15:21:30,926 INFO ipython if test_travel_request:
    # Create a real Employee Advance document
    print(f'Creating Employee Advance with Travel Request {test_travel_request}...')
    
2025-07-08 15:21:30,926 INFO ipython     try:
        advance = frappe.new_doc("Employee Advance")
        advance.employee = test_employee
        advance.company = test_company
        advance.purpose = "Test Travel Advance for Payment Entry"
        advance.advance_amount = 1000
        advance.posting_date = frappe.utils.nowdate()
        advance.travel_request_ref = test_travel_request
        
        # Insert the advance first
        advance.insert()
        print(f'✅ Employee Advance {advance.name} created successfully')
        
        # Now test our payment entry creation function
        from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
        
        print(f'Testing payment entry creation for {advance.name}...')
        
        result = create_payment_entry(advance)
        if result:
            print(f'✅ SUCCESS: Payment Entry {result.name} created!')
            print(f'   - Payment Type: {result.payment_type}')
            print(f'   - Party: {result.party}')
            print(f'   - Amount: {result.paid_amount}')
            print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
        else:
            print('❌ Function returned None')
            
        # Clean up - delete the test advance
        advance.delete()
        print(f'🗑️  Cleaned up test advance {advance.name}')
        
    except Exception as e:
        print(f'❌ ERROR: {str(e)}')
        import traceback
        traceback.print_exc()
else:
    print('❌ Cannot proceed without a Travel Request')
2025-07-08 15:21:30,927 INFO ipython print('✅ Test complete!')
2025-07-08 15:21:30,927 INFO ipython === session end ===
2025-07-08 15:21:57,187 INFO ipython === bench console session ===
2025-07-08 15:21:57,187 INFO ipython #!/usr/bin/env python3
2025-07-08 15:21:57,187 INFO ipython print('🧪 Final Test - Creating Employee Advance with Payment Entry...')
2025-07-08 15:21:57,187 INFO ipython print('=' * 60)
2025-07-08 15:21:57,187 INFO ipython # Get an active employee
2025-07-08 15:21:57,187 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:21:57,187 INFO ipython test_employee = employees[0].name
2025-07-08 15:21:57,188 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:21:57,188 INFO ipython # Get a company
2025-07-08 15:21:57,188 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:21:57,188 INFO ipython test_company = companies[0].name
2025-07-08 15:21:57,188 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:21:57,188 INFO ipython # Get an existing Travel Request
2025-07-08 15:21:57,188 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=1)
2025-07-08 15:21:57,188 INFO ipython test_travel_request = travel_requests[0].name
2025-07-08 15:21:57,188 INFO ipython print(f'Using Travel Request: {test_travel_request}')
2025-07-08 15:21:57,188 INFO ipython # Create Employee Advance
2025-07-08 15:21:57,188 INFO ipython print('Creating Employee Advance...')
2025-07-08 15:21:57,188 INFO ipython advance = frappe.new_doc("Employee Advance")
2025-07-08 15:21:57,188 INFO ipython advance.employee = test_employee
2025-07-08 15:21:57,188 INFO ipython advance.company = test_company
2025-07-08 15:21:57,188 INFO ipython advance.purpose = "Test Travel Advance for Payment Entry"
2025-07-08 15:21:57,188 INFO ipython advance.advance_amount = 1000
2025-07-08 15:21:57,189 INFO ipython advance.posting_date = frappe.utils.nowdate()
2025-07-08 15:21:57,189 INFO ipython advance.travel_request_ref = test_travel_request
2025-07-08 15:21:57,189 INFO ipython # Insert the advance
2025-07-08 15:21:57,189 INFO ipython advance.insert()
2025-07-08 15:21:57,189 INFO ipython print(f'✅ Employee Advance {advance.name} created')
2025-07-08 15:21:57,189 INFO ipython # Test payment entry creation
2025-07-08 15:21:57,189 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:21:57,189 INFO ipython print(f'Testing payment entry creation...')
2025-07-08 15:21:57,189 INFO ipython try:
    result = create_payment_entry(advance)
    if result:
        print(f'✅ SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Amount: {result.paid_amount}')
        print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
        
        # Check if payment entry exists in database
        pe_exists = frappe.db.exists("Payment Entry", result.name)
        print(f'   - Exists in DB: {pe_exists}')
        
    else:
        print('❌ Function returned None')
        
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
2025-07-08 15:21:57,189 INFO ipython # Clean up
2025-07-08 15:21:57,189 INFO ipython advance.delete()
2025-07-08 15:21:57,189 INFO ipython print(f'🗑️ Cleaned up test advance')
2025-07-08 15:21:57,189 INFO ipython print('✅ Test complete!')
2025-07-08 15:21:57,189 INFO ipython === session end ===
2025-07-08 15:22:34,442 INFO ipython === bench console session ===
2025-07-08 15:22:34,442 INFO ipython #!/usr/bin/env python3
2025-07-08 15:22:34,442 INFO ipython print('🧪 Working Test - Creating Valid Employee Advance...')
2025-07-08 15:22:34,443 INFO ipython print('=' * 60)
2025-07-08 15:22:34,443 INFO ipython # Get an active employee
2025-07-08 15:22:34,443 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:22:34,443 INFO ipython test_employee = employees[0].name
2025-07-08 15:22:34,443 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:22:34,443 INFO ipython # Get a company
2025-07-08 15:22:34,443 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:22:34,443 INFO ipython test_company = companies[0].name
2025-07-08 15:22:34,443 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:22:34,444 INFO ipython # Get company currency
2025-07-08 15:22:34,444 INFO ipython company_currency = frappe.get_cached_value("Company", test_company, "default_currency")
2025-07-08 15:22:34,444 INFO ipython print(f'Company currency: {company_currency}')
2025-07-08 15:22:34,444 INFO ipython # Get an existing Travel Request
2025-07-08 15:22:34,444 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=1)
2025-07-08 15:22:34,444 INFO ipython test_travel_request = travel_requests[0].name
2025-07-08 15:22:34,444 INFO ipython print(f'Using Travel Request: {test_travel_request}')
2025-07-08 15:22:34,444 INFO ipython # Create Employee Advance with all required fields
2025-07-08 15:22:34,444 INFO ipython print('Creating Employee Advance with all required fields...')
2025-07-08 15:22:34,444 INFO ipython advance = frappe.new_doc("Employee Advance")
2025-07-08 15:22:34,444 INFO ipython advance.employee = test_employee
2025-07-08 15:22:34,444 INFO ipython advance.company = test_company
2025-07-08 15:22:34,444 INFO ipython advance.purpose = "Test Travel Advance for Payment Entry"
2025-07-08 15:22:34,444 INFO ipython advance.advance_amount = 1000
2025-07-08 15:22:34,444 INFO ipython advance.posting_date = frappe.utils.nowdate()
2025-07-08 15:22:34,445 INFO ipython advance.travel_request_ref = test_travel_request
2025-07-08 15:22:34,445 INFO ipython advance.currency = company_currency
2025-07-08 15:22:34,445 INFO ipython advance.exchange_rate = 1.0  # This was missing!
2025-07-08 15:22:34,445 INFO ipython # Insert the advance
2025-07-08 15:22:34,445 INFO ipython advance.insert()
2025-07-08 15:22:34,445 INFO ipython print(f'✅ Employee Advance {advance.name} created successfully')
2025-07-08 15:22:34,445 INFO ipython # Test payment entry creation
2025-07-08 15:22:34,445 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:22:34,445 INFO ipython print(f'Testing payment entry creation for {advance.name}...')
2025-07-08 15:22:34,445 INFO ipython try:
    result = create_payment_entry(advance)
    if result:
        print(f'✅ SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Amount: {result.paid_amount}')
        print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
        
        # Check if payment entry exists in database
        pe_exists = frappe.db.exists("Payment Entry", result.name)
        print(f'   - Exists in DB: {pe_exists}')
        
        # Check payment entry details
        pe_doc = frappe.get_doc("Payment Entry", result.name)
        print(f'   - Reference No: {pe_doc.reference_no}')
        print(f'   - References: {len(pe_doc.references)} reference(s)')
        if pe_doc.references:
            print(f'     - Reference Type: {pe_doc.references[0].reference_doctype}')
            print(f'     - Reference Name: {pe_doc.references[0].reference_name}')
        
    else:
        print('❌ Function returned None')
        
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
    import traceback
    traceback.print_exc()
2025-07-08 15:22:34,445 INFO ipython # Clean up
2025-07-08 15:22:34,445 INFO ipython try:
    advance.delete()
    print(f'🗑️ Cleaned up test advance')
except:
    print(f'⚠️ Could not delete advance {advance.name}')
2025-07-08 15:22:34,445 INFO ipython print('✅ Test complete!')
2025-07-08 15:22:34,445 INFO ipython === session end ===
2025-07-08 15:23:07,602 INFO ipython === bench console session ===
2025-07-08 15:23:07,603 INFO ipython #!/usr/bin/env python3
2025-07-08 15:23:07,603 INFO ipython print('🧪 Final Working Test - Submitted Employee Advance...')
2025-07-08 15:23:07,603 INFO ipython print('=' * 60)
2025-07-08 15:23:07,603 INFO ipython # Get an active employee
2025-07-08 15:23:07,603 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:23:07,603 INFO ipython test_employee = employees[0].name
2025-07-08 15:23:07,603 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:23:07,603 INFO ipython # Get a company
2025-07-08 15:23:07,603 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:23:07,604 INFO ipython test_company = companies[0].name
2025-07-08 15:23:07,604 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:23:07,604 INFO ipython # Get company currency
2025-07-08 15:23:07,604 INFO ipython company_currency = frappe.get_cached_value("Company", test_company, "default_currency")
2025-07-08 15:23:07,604 INFO ipython print(f'Company currency: {company_currency}')
2025-07-08 15:23:07,604 INFO ipython # Get an existing Travel Request
2025-07-08 15:23:07,604 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=1)
2025-07-08 15:23:07,604 INFO ipython test_travel_request = travel_requests[0].name
2025-07-08 15:23:07,604 INFO ipython print(f'Using Travel Request: {test_travel_request}')
2025-07-08 15:23:07,604 INFO ipython # Create Employee Advance with all required fields
2025-07-08 15:23:07,604 INFO ipython print('Creating and submitting Employee Advance...')
2025-07-08 15:23:07,604 INFO ipython advance = frappe.new_doc("Employee Advance")
2025-07-08 15:23:07,605 INFO ipython advance.employee = test_employee
2025-07-08 15:23:07,605 INFO ipython advance.company = test_company
2025-07-08 15:23:07,605 INFO ipython advance.purpose = "Test Travel Advance for Payment Entry"
2025-07-08 15:23:07,605 INFO ipython advance.advance_amount = 1000
2025-07-08 15:23:07,605 INFO ipython advance.posting_date = frappe.utils.nowdate()
2025-07-08 15:23:07,605 INFO ipython advance.travel_request_ref = test_travel_request
2025-07-08 15:23:07,605 INFO ipython advance.currency = company_currency
2025-07-08 15:23:07,605 INFO ipython advance.exchange_rate = 1.0
2025-07-08 15:23:07,605 INFO ipython # Insert and submit the advance
2025-07-08 15:23:07,605 INFO ipython advance.insert()
2025-07-08 15:23:07,605 INFO ipython print(f'✅ Employee Advance {advance.name} created')
2025-07-08 15:23:07,605 INFO ipython advance.submit()
2025-07-08 15:23:07,605 INFO ipython print(f'✅ Employee Advance {advance.name} submitted (docstatus: {advance.docstatus})')
2025-07-08 15:23:07,606 INFO ipython # Test payment entry creation
2025-07-08 15:23:07,606 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:23:07,606 INFO ipython print(f'Testing payment entry creation for submitted advance...')
2025-07-08 15:23:07,606 INFO ipython try:
    result = create_payment_entry(advance)
    if result:
        print(f'✅ SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Amount: {result.paid_amount}')
        print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
        
        # Check if payment entry exists in database
        pe_exists = frappe.db.exists("Payment Entry", result.name)
        print(f'   - Exists in DB: {pe_exists}')
        
        # Check payment entry details
        pe_doc = frappe.get_doc("Payment Entry", result.name)
        print(f'   - Reference No: {pe_doc.reference_no}')
        print(f'   - References: {len(pe_doc.references)} reference(s)')
        if pe_doc.references:
            print(f'     - Reference Type: {pe_doc.references[0].reference_doctype}')
            print(f'     - Reference Name: {pe_doc.references[0].reference_name}')
            print(f'     - Allocated Amount: {pe_doc.references[0].allocated_amount}')
        
        print('🎉 PAYMENT ENTRY CREATION SUCCESSFUL!')
        
    else:
        print('❌ Function returned None')
        
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
    import traceback
    traceback.print_exc()
2025-07-08 15:23:07,606 INFO ipython print('✅ Test complete!')
2025-07-08 15:23:07,606 INFO ipython print('Note: Employee Advance and Payment Entry left in system for verification')
2025-07-08 15:23:07,606 INFO ipython === session end ===
2025-07-08 15:27:43,977 INFO ipython === bench console session ===
2025-07-08 15:27:43,977 INFO ipython #!/usr/bin/env python3
2025-07-08 15:27:43,977 INFO ipython print('🧪 Final Working Test - Submitted Employee Advance...')
2025-07-08 15:27:43,977 INFO ipython print('=' * 60)
2025-07-08 15:27:43,978 INFO ipython # Get an active employee
2025-07-08 15:27:43,978 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:27:43,978 INFO ipython test_employee = employees[0].name
2025-07-08 15:27:43,978 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:27:43,978 INFO ipython # Get a company
2025-07-08 15:27:43,978 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:27:43,978 INFO ipython test_company = companies[0].name
2025-07-08 15:27:43,978 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:27:43,978 INFO ipython # Get company currency
2025-07-08 15:27:43,978 INFO ipython company_currency = frappe.get_cached_value("Company", test_company, "default_currency")
2025-07-08 15:27:43,978 INFO ipython print(f'Company currency: {company_currency}')
2025-07-08 15:27:43,978 INFO ipython # Get an existing Travel Request
2025-07-08 15:27:43,978 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=1)
2025-07-08 15:27:43,978 INFO ipython test_travel_request = travel_requests[0].name
2025-07-08 15:27:43,978 INFO ipython print(f'Using Travel Request: {test_travel_request}')
2025-07-08 15:27:43,978 INFO ipython # Create Employee Advance with all required fields
2025-07-08 15:27:43,979 INFO ipython print('Creating and submitting Employee Advance...')
2025-07-08 15:27:43,979 INFO ipython advance = frappe.new_doc("Employee Advance")
2025-07-08 15:27:43,979 INFO ipython advance.employee = test_employee
2025-07-08 15:27:43,979 INFO ipython advance.company = test_company
2025-07-08 15:27:43,979 INFO ipython advance.purpose = "Test Travel Advance for Payment Entry"
2025-07-08 15:27:43,979 INFO ipython advance.advance_amount = 1000
2025-07-08 15:27:43,979 INFO ipython advance.posting_date = frappe.utils.nowdate()
2025-07-08 15:27:43,979 INFO ipython advance.travel_request_ref = test_travel_request
2025-07-08 15:27:43,979 INFO ipython advance.currency = company_currency
2025-07-08 15:27:43,979 INFO ipython advance.exchange_rate = 1.0
2025-07-08 15:27:43,979 INFO ipython # Insert and submit the advance
2025-07-08 15:27:43,979 INFO ipython advance.insert()
2025-07-08 15:27:43,979 INFO ipython print(f'✅ Employee Advance {advance.name} created')
2025-07-08 15:27:43,979 INFO ipython advance.submit()
2025-07-08 15:27:43,979 INFO ipython print(f'✅ Employee Advance {advance.name} submitted (docstatus: {advance.docstatus})')
2025-07-08 15:27:43,979 INFO ipython # Test payment entry creation
2025-07-08 15:27:43,979 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:27:43,980 INFO ipython print(f'Testing payment entry creation for submitted advance...')
2025-07-08 15:27:43,980 INFO ipython try:
    result = create_payment_entry(advance)
    if result:
        print(f'✅ SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Amount: {result.paid_amount}')
        print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
        
        # Check if payment entry exists in database
        pe_exists = frappe.db.exists("Payment Entry", result.name)
        print(f'   - Exists in DB: {pe_exists}')
        
        # Check payment entry details
        pe_doc = frappe.get_doc("Payment Entry", result.name)
        print(f'   - Reference No: {pe_doc.reference_no}')
        print(f'   - References: {len(pe_doc.references)} reference(s)')
        if pe_doc.references:
            print(f'     - Reference Type: {pe_doc.references[0].reference_doctype}')
            print(f'     - Reference Name: {pe_doc.references[0].reference_name}')
            print(f'     - Allocated Amount: {pe_doc.references[0].allocated_amount}')
        
        print('🎉 PAYMENT ENTRY CREATION SUCCESSFUL!')
        
    else:
        print('❌ Function returned None')
        
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
    import traceback
    traceback.print_exc()
2025-07-08 15:27:43,980 INFO ipython print('✅ Test complete!')
2025-07-08 15:27:43,980 INFO ipython print('Note: Employee Advance and Payment Entry left in system for verification')
2025-07-08 15:27:43,980 INFO ipython === session end ===
2025-07-08 15:28:15,684 INFO ipython === bench console session ===
2025-07-08 15:28:15,684 INFO ipython #!/usr/bin/env python3
2025-07-08 15:28:15,684 INFO ipython print('🧪 Quick Test - Updated Hook...')
2025-07-08 15:28:15,684 INFO ipython print('=' * 40)
2025-07-08 15:28:15,684 INFO ipython # Test the updated hook function directly
2025-07-08 15:28:15,685 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:28:15,685 INFO ipython # Get existing submitted Employee Advance
2025-07-08 15:28:15,685 INFO ipython existing_advances = frappe.get_all('Employee Advance', 
                                 filters={'docstatus': 1, 'travel_request_ref': ['!=', '']}, 
                                 limit=1)
2025-07-08 15:28:15,685 INFO ipython if existing_advances:
    advance_name = existing_advances[0].name
    print(f'Testing with existing advance: {advance_name}')
    
2025-07-08 15:28:15,685 INFO ipython     # Get the advance document
2025-07-08 15:28:15,685 INFO ipython     advance = frappe.get_doc('Employee Advance', advance_name)
2025-07-08 15:28:15,685 INFO ipython     try:
        result = create_payment_entry(advance)
        if result:
            print(f'✅ SUCCESS: Payment Entry {result.name} created!')
            print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
            print(f'   - Amount: {result.paid_amount}')
        else:
            print('❌ Function returned None')
    except Exception as e:
        print(f'❌ ERROR: {str(e)}')
        
2025-07-08 15:28:15,685 INFO ipython else:
    print('❌ No submitted Employee Advance with travel_request_ref found')
2025-07-08 15:28:15,685 INFO ipython print('✅ Test complete!')
2025-07-08 15:28:15,685 INFO ipython === session end ===
2025-07-08 15:28:45,981 INFO ipython === bench console session ===
2025-07-08 15:28:45,981 INFO ipython #!/usr/bin/env python3
2025-07-08 15:28:45,982 INFO ipython print('🔍 Debug Payment Entry Creation...')
2025-07-08 15:28:45,982 INFO ipython print('=' * 40)
2025-07-08 15:28:45,982 INFO ipython # Get existing submitted Employee Advance
2025-07-08 15:28:45,982 INFO ipython existing_advances = frappe.get_all('Employee Advance', 
                                 filters={'docstatus': 1, 'travel_request_ref': ['!=', '']}, 
                                 limit=1)
2025-07-08 15:28:45,982 INFO ipython if existing_advances:
    advance_name = existing_advances[0].name
    print(f'Debugging with advance: {advance_name}')
    
2025-07-08 15:28:45,982 INFO ipython     # Get the advance document
2025-07-08 15:28:45,982 INFO ipython     advance = frappe.get_doc('Employee Advance', advance_name)
2025-07-08 15:28:45,982 INFO ipython     print(f'Advance Amount: {advance.advance_amount}')
2025-07-08 15:28:45,982 INFO ipython     print(f'Paid Amount: {advance.paid_amount}')
2025-07-08 15:28:45,982 INFO ipython     print(f'Currency: {advance.currency}')
2025-07-08 15:28:45,982 INFO ipython     # Test payment entry creation step by step
2025-07-08 15:28:45,983 INFO ipython     from hrms.overrides.employee_payment_entry import get_payment_entry_for_employee
2025-07-08 15:28:45,983 INFO ipython     # Set Administrator context
2025-07-08 15:28:45,983 INFO ipython     frappe.set_user("Administrator")
2025-07-08 15:28:45,983 INFO ipython     try:
        payment_entry = get_payment_entry_for_employee("Employee Advance", advance.name)
        
        print(f'Payment Entry created:')
        print(f'  - Payment Type: {payment_entry.payment_type}')
        print(f'  - Party: {payment_entry.party}')
        print(f'  - Paid Amount: {payment_entry.paid_amount}')
        print(f'  - Received Amount: {payment_entry.received_amount}')
        print(f'  - Paid From: {payment_entry.paid_from}')
        print(f'  - Paid To: {payment_entry.paid_to}')
        
        # Check references
        if payment_entry.references:
            ref = payment_entry.references[0]
            print(f'  - Reference: {ref.reference_doctype} {ref.reference_name}')
            print(f'  - Total Amount: {ref.total_amount}')
            print(f'  - Outstanding Amount: {ref.outstanding_amount}')
            print(f'  - Allocated Amount: {ref.allocated_amount}')
        
        # Check if payment_approval_group field exists
        if hasattr(payment_entry, 'payment_approval_group'):
            print(f'  - Payment Approval Group: {payment_entry.payment_approval_group}')
        else:
            print('  - No payment_approval_group field found')
            
    except Exception as e:
        print(f'❌ ERROR: {str(e)}')
        import traceback
        traceback.print_exc()
        
2025-07-08 15:28:45,983 INFO ipython else:
    print('❌ No submitted Employee Advance found')
2025-07-08 15:28:45,983 INFO ipython print('✅ Debug complete!')
2025-07-08 15:28:45,983 INFO ipython === session end ===
2025-07-08 15:29:24,298 INFO ipython === bench console session ===
2025-07-08 15:29:24,298 INFO ipython #!/usr/bin/env python3
2025-07-08 15:29:24,298 INFO ipython print('🧪 Test with Fresh Unpaid Employee Advance...')
2025-07-08 15:29:24,298 INFO ipython print('=' * 50)
2025-07-08 15:29:24,298 INFO ipython # Get an active employee
2025-07-08 15:29:24,298 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:29:24,298 INFO ipython test_employee = employees[0].name
2025-07-08 15:29:24,298 INFO ipython print(f'Using employee: {test_employee}')
2025-07-08 15:29:24,298 INFO ipython # Get a company
2025-07-08 15:29:24,299 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:29:24,299 INFO ipython test_company = companies[0].name
2025-07-08 15:29:24,299 INFO ipython print(f'Using company: {test_company}')
2025-07-08 15:29:24,299 INFO ipython # Get company currency
2025-07-08 15:29:24,299 INFO ipython company_currency = frappe.get_cached_value("Company", test_company, "default_currency")
2025-07-08 15:29:24,299 INFO ipython # Get an existing Travel Request
2025-07-08 15:29:24,299 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=1)
2025-07-08 15:29:24,299 INFO ipython test_travel_request = travel_requests[0].name
2025-07-08 15:29:24,299 INFO ipython print(f'Using Travel Request: {test_travel_request}')
2025-07-08 15:29:24,299 INFO ipython # Create a fresh Employee Advance
2025-07-08 15:29:24,299 INFO ipython print('Creating fresh Employee Advance...')
2025-07-08 15:29:24,299 INFO ipython advance = frappe.new_doc("Employee Advance")
2025-07-08 15:29:24,299 INFO ipython advance.employee = test_employee
2025-07-08 15:29:24,299 INFO ipython advance.company = test_company
2025-07-08 15:29:24,299 INFO ipython advance.purpose = "Test Fresh Advance for Payment Entry"
2025-07-08 15:29:24,299 INFO ipython advance.advance_amount = 5000  # Smaller amount for testing
2025-07-08 15:29:24,300 INFO ipython advance.posting_date = frappe.utils.nowdate()
2025-07-08 15:29:24,300 INFO ipython advance.travel_request_ref = test_travel_request
2025-07-08 15:29:24,300 INFO ipython advance.currency = company_currency
2025-07-08 15:29:24,300 INFO ipython advance.exchange_rate = 1.0
2025-07-08 15:29:24,300 INFO ipython # Insert and submit
2025-07-08 15:29:24,300 INFO ipython advance.insert()
2025-07-08 15:29:24,300 INFO ipython advance.submit()
2025-07-08 15:29:24,300 INFO ipython print(f'✅ Fresh Employee Advance {advance.name} created and submitted')
2025-07-08 15:29:24,300 INFO ipython print(f'   - Advance Amount: {advance.advance_amount}')
2025-07-08 15:29:24,300 INFO ipython print(f'   - Paid Amount: {advance.paid_amount}')
2025-07-08 15:29:24,300 INFO ipython print(f'   - Outstanding: {advance.advance_amount - advance.paid_amount}')
2025-07-08 15:29:24,300 INFO ipython # Now test our hook
2025-07-08 15:29:24,300 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-08 15:29:24,300 INFO ipython print(f'Testing payment entry creation...')
2025-07-08 15:29:24,300 INFO ipython try:
    result = create_payment_entry(advance)
    if result:
        print(f'🎉 SUCCESS: Payment Entry {result.name} created!')
        print(f'   - Payment Type: {result.payment_type}')
        print(f'   - Party: {result.party}')
        print(f'   - Paid Amount: {result.paid_amount}')
        print(f'   - Status: {"Submitted" if result.docstatus == 1 else "Draft"}')
        
        # Verify it exists in database
        pe_exists = frappe.db.exists("Payment Entry", result.name)
        print(f'   - Exists in DB: {pe_exists}')
        
        print('🎉 HOOK IS WORKING PERFECTLY!')
        
    else:
        print('❌ Function returned None')
        
except Exception as e:
    print(f'❌ ERROR: {str(e)}')
2025-07-08 15:29:24,300 INFO ipython print('✅ Test complete!')
2025-07-08 15:29:24,301 INFO ipython print(f'Note: Employee Advance {advance.name} and Payment Entry left in system')
2025-07-08 15:29:24,301 INFO ipython === session end ===
2025-07-08 15:31:09,534 INFO ipython === bench console session ===
2025-07-08 15:31:09,534 INFO ipython #!/usr/bin/env python3
2025-07-08 15:31:09,534 INFO ipython print('🚀 Creating Employee Advances with Payment Entries...')
2025-07-08 15:31:09,534 INFO ipython print('=' * 60)
2025-07-08 15:31:09,534 INFO ipython # Get active employees
2025-07-08 15:31:09,534 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=5, fields=['name', 'employee_name'])
2025-07-08 15:31:09,534 INFO ipython print(f'Found {len(employees)} active employees')
2025-07-08 15:31:09,535 INFO ipython # Get companies
2025-07-08 15:31:09,535 INFO ipython companies = frappe.get_all('Company', limit=3, fields=['name', 'default_currency'])
2025-07-08 15:31:09,535 INFO ipython test_company = companies[0].name
2025-07-08 15:31:09,535 INFO ipython company_currency = companies[0].default_currency
2025-07-08 15:31:09,535 INFO ipython print(f'Using company: {test_company} (Currency: {company_currency})')
2025-07-08 15:31:09,535 INFO ipython # Get existing Travel Requests
2025-07-08 15:31:09,535 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=10, fields=['name', 'employee'])
2025-07-08 15:31:09,535 INFO ipython print(f'Found {len(travel_requests)} Travel Requests')
2025-07-08 15:31:09,535 INFO ipython created_advances = []
2025-07-08 15:31:09,535 INFO ipython created_payment_entries = []
2025-07-08 15:31:09,535 INFO ipython # Create Employee Advances for different employees
2025-07-08 15:31:09,535 INFO ipython for i, employee in enumerate(employees[:3]):  # Create for first 3 employees
    try:
        print(f'\n📝 Creating Employee Advance {i+1}/3 for {employee.employee_name}...')
        
        # Find a travel request for this employee or use any available
        employee_travel_request = None
        for tr in travel_requests:
            if tr.employee == employee.name:
                employee_travel_request = tr.name
                break
        
        if not employee_travel_request and travel_requests:
            employee_travel_request = travel_requests[0].name
            
        if not employee_travel_request:
            print(f'❌ No Travel Request available for {employee.employee_name}')
            continue
            
        # Create Employee Advance
        advance = frappe.new_doc("Employee Advance")
        advance.employee = employee.name
        advance.company = test_company
        advance.purpose = f"Travel Advance for {employee.employee_name} - Test {i+1}"
        advance.advance_amount = (i+1) * 2500  # Different amounts: 2500, 5000, 7500
        advance.posting_date = frappe.utils.nowdate()
        advance.travel_request_ref = employee_travel_request
        advance.currency = company_currency
        advance.exchange_rate = 1.0
        
        # Insert and submit
        advance.insert()
        print(f'✅ Employee Advance {advance.name} created')
        
        advance.submit()
        print(f'✅ Employee Advance {advance.name} submitted')
        print(f'   - Employee: {employee.employee_name}')
        print(f'   - Amount: {advance.advance_amount} {advance.currency}')
        print(f'   - Travel Request: {employee_travel_request}')
        
        created_advances.append({
            'name': advance.name,
            'employee': employee.employee_name,
            'amount': advance.advance_amount,
            'currency': advance.currency
        })
        
        # Check if payment entry was created by the hook
        payment_entries = frappe.get_all("Payment Entry", 
                                       filters={"reference_no": advance.name},
                                       fields=["name", "docstatus", "paid_amount", "party"])
        
        if payment_entries:
            for pe in payment_entries:
                print(f'🎉 Payment Entry {pe.name} created automatically!')
                print(f'   - Amount: {pe.paid_amount} (Status: {"Submitted" if pe.docstatus == 1 else "Draft"})')
                
                created_payment_entries.append({
                    'name': pe.name,
                    'advance': advance.name,
                    'amount': pe.paid_amount,
                    'status': 'Submitted' if pe.docstatus == 1 else 'Draft'
                })
        else:
            print(f'⚠️  No Payment Entry found for {advance.name}')
            
    except Exception as e:
        print(f'❌ ERROR creating advance for {employee.employee_name}: {str(e)}')
2025-07-08 15:31:09,535 INFO ipython # Summary
2025-07-08 15:31:09,536 INFO ipython print(f'\n📊 SUMMARY')
2025-07-08 15:31:09,536 INFO ipython print('=' * 40)
2025-07-08 15:31:09,536 INFO ipython print(f'✅ Employee Advances Created: {len(created_advances)}')
2025-07-08 15:31:09,536 INFO ipython for adv in created_advances:
    print(f'   - {adv["name"]}: {adv["employee"]} - {adv["amount"]} {adv["currency"]}')
2025-07-08 15:31:09,536 INFO ipython print(f'\n✅ Payment Entries Created: {len(created_payment_entries)}')
2025-07-08 15:31:09,536 INFO ipython for pe in created_payment_entries:
    print(f'   - {pe["name"]}: {pe["amount"]} ({pe["status"]}) for {pe["advance"]}')
2025-07-08 15:31:09,536 INFO ipython if created_advances and created_payment_entries:
    print(f'\n🎉 SUCCESS! Hook is working perfectly!')
    print(f'   - {len(created_advances)} Employee Advances created and submitted')
    print(f'   - {len(created_payment_entries)} Payment Entries created automatically')
else:
    print(f'\n⚠️  Some issues occurred during creation')
2025-07-08 15:31:09,536 INFO ipython print(f'\n✅ Test complete!')
2025-07-08 15:31:09,536 INFO ipython === session end ===
2025-07-08 15:41:25,008 INFO ipython === bench console session ===
2025-07-08 15:41:25,008 INFO ipython print('🧪 Testing Employee Advance Payment Hook in Console...')
2025-07-08 15:41:25,008 INFO ipython print('=' * 60)
2025-07-08 15:41:25,008 INFO ipython # Test 1: Check if hook can be imported
2025-07-08 15:41:25,008 INFO ipython try:
    from csf_tz.csftz_hooks.employee_advance_payment_and_expense import execute, create_payment_entry
    print('✅ Hook imported successfully')
except Exception as e:
    print(f'❌ Import failed: {e}')
    exit()
2025-07-08 15:41:25,008 INFO ipython # Test 2: Get test data
2025-07-08 15:41:25,008 INFO ipython employees = frappe.get_all('Employee', filters={'status': 'Active'}, limit=1)
2025-07-08 15:41:25,008 INFO ipython companies = frappe.get_all('Company', limit=1)
2025-07-08 15:41:25,009 INFO ipython travel_requests = frappe.get_all('Travel Request', limit=1)
2025-07-08 15:41:25,009 INFO ipython if not employees or not companies or not travel_requests:
    print('❌ Missing required data (employees, companies, or travel requests)')
    exit()
2025-07-08 15:41:25,009 INFO ipython test_employee = employees[0].name
2025-07-08 15:41:25,009 INFO ipython test_company = companies[0].name
2025-07-08 15:41:25,009 INFO ipython test_travel_request = travel_requests[0].name
2025-07-08 15:41:25,009 INFO ipython print(f'Using Employee: {test_employee}')
2025-07-08 15:41:25,009 INFO ipython print(f'Using Company: {test_company}')
2025-07-08 15:41:25,009 INFO ipython print(f'Using Travel Request: {test_travel_request}')
2025-07-08 15:41:25,009 INFO ipython # Test 3: Create Employee Advance
2025-07-08 15:41:25,009 INFO ipython print('\n📝 Creating Employee Advance...')
2025-07-08 15:41:25,009 INFO ipython advance = frappe.new_doc('Employee Advance')
2025-07-08 15:41:25,009 INFO ipython advance.employee = test_employee
2025-07-08 15:41:25,010 INFO ipython advance.company = test_company
2025-07-08 15:41:25,010 INFO ipython advance.purpose = 'Console Test Travel Advance'
2025-07-08 15:41:25,010 INFO ipython advance.advance_amount = 3000
2025-07-08 15:41:25,010 INFO ipython advance.posting_date = frappe.utils.nowdate()
2025-07-08 15:41:25,010 INFO ipython advance.travel_request_ref = test_travel_request
2025-07-08 15:41:25,010 INFO ipython advance.currency = frappe.get_cached_value('Company', test_company, 'default_currency')
2025-07-08 15:41:25,010 INFO ipython advance.exchange_rate = 1.0
2025-07-08 15:41:25,010 INFO ipython advance.insert()
2025-07-08 15:41:25,010 INFO ipython print(f'✅ Employee Advance {advance.name} created')
2025-07-08 15:41:25,010 INFO ipython # Test 4: Submit and trigger hook
2025-07-08 15:41:25,010 INFO ipython print(f'\n📤 Submitting Employee Advance (this will trigger the hook)...')
2025-07-08 15:41:25,010 INFO ipython try:
    advance.submit()
    print(f'✅ Employee Advance {advance.name} submitted successfully')
    
    # Test 5: Check if payment entry was created
    payment_entries = frappe.get_all('Payment Entry', 
                                   filters={'reference_no': advance.name},
                                   fields=['name', 'docstatus', 'paid_amount', 'party'])
    
    if payment_entries:
        print(f'\n🎉 SUCCESS! Payment Entry created:')
        for pe in payment_entries:
            print(f'   - Payment Entry: {pe.name}')
            print(f'   - Amount: {pe.paid_amount}')
            print(f'   - Party: {pe.party}')
            print(f'   - Status: {"Submitted" if pe.docstatus == 1 else "Draft"}')
            
        print(f'\n✅ HOOK IS WORKING PERFECTLY!')
    else:
        print(f'\n❌ No Payment Entry found for {advance.name}')
        
except Exception as e:
    print(f'❌ Error during submission: {e}')
    import traceback
    traceback.print_exc()
2025-07-08 15:41:25,011 INFO ipython print(f'\n✅ Console test complete!')
2025-07-08 15:41:25,011 INFO ipython === session end ===
2025-07-09 09:58:36,203 INFO ipython === bench console session ===
2025-07-09 09:58:36,205 INFO ipython # Test the permission bypass functionality
2025-07-09 09:58:36,205 INFO ipython import frappe
2025-07-09 09:58:36,205 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-09 09:58:36,205 INFO ipython # Get a sample employee advance document
2025-07-09 09:58:36,205 INFO ipython advance = frappe.get_doc("Employee Advance", {"docstatus": 1, "travel_request_ref": ["!=", ""]})
2025-07-09 09:58:36,206 INFO ipython if advance:
        print(f"Testing with Employee Advance: {advance.name}")
            try:
                        result = create_payment_entry(advance)
2025-07-09 09:58:36,206 INFO ipython         print(f"Success: Payment Entry created - {result.name if result else 'None'}")
2025-07-09 09:58:36,206 INFO ipython     except Exception as e:
            print(f"Error: {str(e)}")
2025-07-09 09:58:36,206 INFO ipython else:
        print("No suitable Employee Advance found for testing")
2025-07-09 09:58:36,206 INFO ipython # Let's find an existing employee advance first
2025-07-09 09:58:36,206 INFO ipython advances = frappe.get_list("Employee Advance", filters={"docstatus": 1, "travel_request_ref": ["!=", ""]}, limit=1)
2025-07-09 09:58:36,206 INFO ipython print(f"Found advances: {advances}")
2025-07-09 09:58:36,206 INFO ipython # Check for any employee advances
2025-07-09 09:58:36,206 INFO ipython all_advances = frappe.get_list("Employee Advance", limit=5)
2025-07-09 09:58:36,207 INFO ipython print(f"All advances: {all_advances}")
2025-07-09 09:58:36,207 INFO ipython # Check for submitted advances
2025-07-09 09:58:36,207 INFO ipython submitted_advances = frappe.get_list("Employee Advance", filters={"docstatus": 1}, limit=5)
2025-07-09 09:58:36,207 INFO ipython print(f"Submitted advances: {submitted_advances}")
2025-07-09 09:58:36,207 INFO ipython === session end ===
2025-07-09 14:28:43,879 INFO ipython === bench console session ===
2025-07-09 14:28:43,882 INFO ipython # Check if there are any custom fields for Sales Order that include 'student'
2025-07-09 14:28:43,882 INFO ipython import frappe
2025-07-09 14:28:43,882 INFO ipython # Check custom fields for Sales Order
2025-07-09 14:28:43,882 INFO ipython custom_fields = frappe.get_all("Custom Field", 
    filters={"dt": "Sales Order"}, 
        fields=["fieldname", "label", "fieldtype", "options"])
2025-07-09 14:28:43,882 INFO ipython print("Custom fields for Sales Order:")
2025-07-09 14:28:43,882 INFO ipython for field in custom_fields:
        print(f"- {field.fieldname}: {field.label} ({field.fieldtype})")
            if field.options:
                        print(f"  Options: {field.options}")
2025-07-09 14:28:43,882 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all mariadb
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all migrate
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all migrate
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all console
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all execute csf_tz.patches.add_student_field_to_sales_order.execute
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && python fix_student_field.py
2025-07-09 14:28:43,883 INFO ipython === session end ===
2025-07-09 17:28:08,978 INFO ipython === bench console session ===
2025-07-09 17:28:08,979 INFO ipython === session end ===
2025-07-13 10:38:51,117 INFO ipython === bench console session ===
2025-07-13 10:38:51,117 INFO ipython # Test if we can query the Employee Salary Component Limit table without errors
2025-07-13 10:38:51,117 INFO ipython import frappe
2025-07-13 10:38:51,117 INFO ipython frappe.db.sql("SELECT name, parent, parentfield, parenttype FROM `tabEmployee Salary Component Limit` LIMIT 5")
2025-07-13 10:38:51,118 INFO ipython # Test Employee deletion check - this should work now without the "Unknown column 'parent'" error
2025-07-13 10:38:51,118 INFO ipython frappe.db.get_values("Employee Salary Component Limit", {"parent": "HR-EMP-00024"}, ["name", "salary_component"], as_dict=True)
2025-07-13 10:38:51,118 INFO ipython === session end ===
2025-07-14 15:21:05,747 INFO ipython === bench console session ===
2025-07-14 15:21:05,748 INFO ipython # Test the approve function
2025-07-14 15:21:05,748 INFO ipython import frappe
2025-07-14 15:21:05,748 INFO ipython from csf_tz.csf_tz.csf_tz.report.salary_register_csf.salary_register_csf import approve
2025-07-14 15:21:05,748 INFO ipython # Test with sample data
2025-07-14 15:21:05,749 INFO ipython test_data = [
    {"salary_slip_id": "SS-2024-001", "employee": "EMP-001"},
        {"salary_slip_id": "Total", "employee": ""},  # This should be filtered out
        ]
2025-07-14 15:21:05,749 INFO ipython result = approve(test_data)
2025-07-14 15:21:05,749 INFO ipython print("Result:", result)
2025-07-14 15:21:05,749 INFO ipython # Test the function using frappe.call
2025-07-14 15:21:05,749 INFO ipython result = frappe.call("csf_tz.csf_tz.csf_tz.report.salary_register_csf.salary_register_csf.approve", data='[{"salary_slip_id": "test"}]')
2025-07-14 15:21:05,749 INFO ipython print("Function call result:", result)
2025-07-14 15:21:05,749 INFO ipython === session end ===
2025-08-10 12:46:27,873 INFO ipython === bench console session ===
2025-08-10 12:46:27,874 INFO ipython frappe.get_all("Clearing File", limit=5)
2025-08-10 12:46:27,875 INFO ipython frappe.get_doc("DocType", "Clearing File").permissions
2025-08-10 12:46:27,875 INFO ipython for perm in frappe.get_doc("DocType", "Clearing File").permissions: print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}")
2025-08-10 12:46:27,875 INFO ipython frappe.session.user
2025-08-10 12:46:27,875 INFO ipython === session end ===
2025-08-10 12:53:24,425 INFO ipython === bench console session ===
2025-08-10 12:53:24,425 INFO ipython frappe.get_doc("User", "<EMAIL>").roles
2025-08-10 12:53:24,426 INFO ipython user_roles = [role.role for role in frappe.get_doc("User", "<EMAIL>").roles]
2025-08-10 12:53:24,426 INFO ipython print("User roles:", user_roles)
2025-08-10 12:53:24,426 INFO ipython print("Has System Manager:", "System Manager" in user_roles)
2025-08-10 12:53:24,426 INFO ipython # Check user permissions for Clearing File
2025-08-10 12:53:24,426 INFO ipython user_perms = frappe.get_all("User Permission", filters={"user": "<EMAIL>", "allow": "Clearing File"})
2025-08-10 12:53:24,426 INFO ipython print("User Permissions for Clearing File:", user_perms)
2025-08-10 12:53:24,426 INFO ipython # Check if there are any company restrictions
2025-08-10 12:53:24,426 INFO ipython company_perms = frappe.get_all("User Permission", filters={"user": "<EMAIL>", "allow": "Company"})
2025-08-10 12:53:24,426 INFO ipython print("Company Permissions:", company_perms)
2025-08-10 12:53:24,427 INFO ipython # Set user context to simulate login
2025-08-10 12:53:24,427 INFO ipython frappe.set_user("<EMAIL>")
2025-08-10 12:53:24,427 INFO ipython print("Current user:", frappe.session.user)
2025-08-10 12:53:24,427 INFO ipython # Try to get clearing files as this user
2025-08-10 12:53:24,427 INFO ipython try:
        clearing_files = frappe.get_all("Clearing File", limit=5, fields=["name", "customer", "status"])
            print("Clearing files accessible:", len(clearing_files))
2025-08-10 12:53:24,427 INFO ipython     for cf in clearing_files:
            print(f"  - {cf.name}: {cf.customer} ({cf.status})")
        except Exception as e:
2025-08-10 12:53:24,427 INFO ipython     print("Error accessing clearing files:", str(e))
2025-08-10 12:53:24,427 INFO ipython try:
        clearing_files = frappe.get_all("Clearing File", limit=5, fields=["name", "customer", "status"])
            print("Clearing files accessible:", len(clearing_files))
2025-08-10 12:53:24,427 INFO ipython     for cf in clearing_files:
            print(f"  - {cf.name}: {cf.customer} ({cf.status})")
        except Exception as e:
2025-08-10 12:53:24,427 INFO ipython     print("Error accessing clearing files:", str(e))
2025-08-10 12:53:24,428 INFO ipython clearing_files = frappe.get_all("Clearing File", limit=5, fields=["name", "customer", "status"])
2025-08-10 12:53:24,428 INFO ipython print("Clearing files accessible:", len(clearing_files))
2025-08-10 12:53:24,428 INFO ipython # Check if the clearing module is visible to the user
2025-08-10 12:53:24,428 INFO ipython modules = frappe.get_all("Module Def", fields=["name", "app_name"])
2025-08-10 12:53:24,428 INFO ipython clearing_module = [m for m in modules if m.app_name == "clearing"]
2025-08-10 12:53:24,428 INFO ipython print("Clearing modules:", clearing_module)
2025-08-10 12:53:24,428 INFO ipython # Check workspace permissions
2025-08-10 12:53:24,428 INFO ipython workspace = frappe.get_doc("Workspace", "Clearing & Forwarding")
2025-08-10 12:53:24,428 INFO ipython print("Workspace public:", workspace.public)
2025-08-10 12:53:24,428 INFO ipython print("Workspace roles:", workspace.roles)
2025-08-10 12:53:24,429 INFO ipython # Check if there are any default filters or restrictions
2025-08-10 12:53:24,429 INFO ipython doctype_meta = frappe.get_meta("Clearing File")
2025-08-10 12:53:24,429 INFO ipython print("Clearing File has_web_view:", doctype_meta.has_web_view)
2025-08-10 12:53:24,429 INFO ipython print("Clearing File is_submittable:", doctype_meta.is_submittable)
2025-08-10 12:53:24,429 INFO ipython print("Clearing File track_changes:", doctype_meta.track_changes)
2025-08-10 12:53:24,429 INFO ipython # Check if user has any saved filters that might be causing issues
2025-08-10 12:53:24,429 INFO ipython saved_filters = frappe.get_all("List Filter", filters={"for_user": "<EMAIL>", "reference_doctype": "Clearing File"})
2025-08-10 12:53:24,429 INFO ipython print("Saved filters for Clearing File:", saved_filters)
2025-08-10 12:53:24,429 INFO ipython # Check if there are any custom list view settings
2025-08-10 12:53:24,429 INFO ipython list_settings = frappe.get_all("List View Settings", filters={"name": "Clearing File"})
2025-08-10 12:53:24,429 INFO ipython print("List View Settings:", list_settings)
2025-08-10 12:53:24,430 INFO ipython # Check if there are any custom permissions or restrictions in the clearing file python file
2025-08-10 12:53:24,430 INFO ipython === session end ===
2025-08-12 17:04:44,145 INFO ipython === bench console session ===
2025-08-12 17:04:44,146 INFO ipython frappe.db.get_table_columns("tabEmployee Piecework Additional Salary")
2025-08-12 17:04:44,146 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Employee Piecework%'")
2025-08-12 17:04:44,146 INFO ipython frappe.db.sql("DESCRIBE `tabEmployee Piecework Additional Salary`")
2025-08-12 17:04:44,146 INFO ipython === session end ===
