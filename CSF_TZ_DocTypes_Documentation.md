# CSF TZ - DocTypes Documentation

## Overview

This document provides comprehensive information about all DocTypes in the CSF TZ application. The application contains over 200 custom DocTypes organized across 12 major modules, making it one of the most comprehensive Frappe applications available.

## DocTypes Summary

### Total DocTypes: 200+
The CSF TZ application includes custom DocTypes organized across the following modules:

| Module | DocType Count | Primary Purpose |
|--------|---------------|-----------------|
| Core CSF TZ | 85+ | Core business functionality and settings |
| Fleet Management | 45+ | Vehicle fleet management and maintenance |
| Clearing and Forwarding | 25+ | Import/export and customs management |
| After Sales Services | 12+ | Post-sale service management |
| Workshop Management | 8+ | Workshop and maintenance services |
| Sales and Marketing | 8+ | Sales analytics and customer management |
| Purchase and Stock Management | 6+ | Enhanced inventory management |
| Meal Count | 5+ | Employee meal tracking |
| Feedback System | 4+ | Customer feedback collection |
| Stanbic Bank Integration | 3+ | Banking integration |
| AI Integration | 2+ | AI and machine learning features |
| Auth OTP | 2+ | OTP authentication system |

## Module 1: Core CSF TZ (85+ DocTypes)

### System Configuration DocTypes

#### 1. CSF TZ Settings
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/csf_tz_settings/`
**Type:** Single DocType (System Configuration)
**Purpose:** Central configuration hub for all CSF TZ functionality

**Key Configuration Sections:**
- **System Behavior:** Unique records, validation settings, auto-POS configuration
- **Payroll Overrides:** Fixed working days, payroll approval, salary slip customizations
- **HR Settings:** Overtime calculation, shift details override
- **Trade-in Settings:** Enable/disable trade-in functionality
- **TZ Regions Data:** Tanzania geographic data population

**Critical Fields:**
- `enable_trade_in`: Enables trade-in functionality across the system
- `enable_payroll_approval`: Activates payroll approval workflow
- `enable_overtime_calculation`: Enables automatic overtime calculations
- `populate_tz_regions`: One-time population of Tanzania geographic data

#### 2. Visibility
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/visibility/`
**Type:** Master DocType
**Purpose:** Universal field visibility control system

**Functionality:**
- Controls field visibility across all DocTypes
- Rule-based visibility engine
- Performance-optimized visibility processing
- Conditional field display based on user roles and data

#### 3. SQL Process
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/sql_process/`
**Type:** Transaction DocType
**Purpose:** Secure SQL command execution with audit trail

**Security Features:**
- Role-based SQL execution permissions
- Command validation and sanitization
- Execution audit trail
- Result logging and monitoring

### Human Resources DocTypes

#### 4. Piecework
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/piecework/`
**Type:** Transaction DocType
**Auto-naming:** `format:{date}-{task}-{######}`
**Purpose:** Task-based work tracking and payment calculation

**Key Fields:**
- `date`: Work completion date
- `task`: Link to Piecework Type
- `quantity`: Work quantity completed
- `task_rate`: Rate per unit of work
- `total`: Calculated total payment
- `employees`: Multi-employee assignment table

**Business Logic:**
- Automatic total calculation based on quantity and rate
- Multi-employee work distribution
- Integration with payroll processing

#### 5. Piecework Type
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/piecework_type/`
**Type:** Master DocType
**Purpose:** Defines types of piecework tasks and their rates

**Key Fields:**
- `task_name`: Descriptive name of the task
- `rate`: Standard rate per unit
- `description`: Detailed task description
- `uom`: Unit of measurement for the task

#### 6. Additional Salary Component
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/additional_salary_component/`
**Type:** Master DocType
**Purpose:** Defines additional salary components for payroll processing

**Component Types:**
- Bonuses and incentives
- Allowances and benefits
- Deductions and penalties
- One-time payments

#### 7. Payroll Approval
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/payroll_approval/`
**Type:** Transaction DocType
**Purpose:** Multi-level payroll approval workflow

**Approval Features:**
- Hierarchical approval process
- Approval status tracking
- Rejection handling with comments
- Audit trail maintenance

### Financial Management DocTypes

#### 8. Bank Charges
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/bank_charges/`
**Type:** Transaction DocType
**Purpose:** Automatic bank charge calculation and posting

**Functionality:**
- Automatic charge calculation based on transaction type
- Integration with Payment Entry
- GL entry creation for bank charges
- Multi-currency support

#### 9. Payment Reconciliation
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/payment_reconciliation/`
**Type:** Transaction DocType
**Purpose:** Enhanced payment reconciliation with local banking features

**Features:**
- Bank statement import and matching
- Automatic reconciliation rules
- Exception handling and reporting
- Integration with Stanbic Bank systems

#### 10. Withholding Tax
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/withholding_tax/`
**Type:** Master DocType
**Purpose:** Tanzania withholding tax configuration and calculation

**Tax Management:**
- Supplier-specific tax rates
- Automatic tax calculation on purchases
- Tax certificate generation
- Compliance reporting

### Inventory Management DocTypes

#### 11. Delivery Exchange
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/delivery_exchange/`
**Type:** Transaction DocType
**Purpose:** Handles product exchanges and returns

**Exchange Process:**
- Original item return processing
- Replacement item delivery
- Price difference calculations
- Stock movement tracking

#### 12. Stock Transfer
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/stock_transfer/`
**Type:** Transaction DocType
**Purpose:** Enhanced stock transfer with approval workflow

**Transfer Features:**
- Multi-location transfers
- Approval workflow integration
- Transit tracking
- Automatic GL entries

### Geographic Data DocTypes

#### 13. TZ Region
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/tz_region/`
**Type:** Master DocType
**Purpose:** Tanzania regions master data

#### 14. TZ District
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/tz_district/`
**Type:** Master DocType
**Purpose:** Tanzania districts master data

#### 15. TZ Ward
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/tz_ward/`
**Type:** Master DocType
**Purpose:** Tanzania wards master data

#### 16. TZ Village
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/tz_village/`
**Type:** Master DocType
**Purpose:** Tanzania villages master data

**Geographic Hierarchy:**
```
TZ Region
├── TZ District
    ├── TZ Ward
        └── TZ Village
```

### Insurance Management DocTypes

#### 17. TZ Insurance Cover Note
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/tz_insurance_cover_note/`
**Type:** Transaction DocType
**Purpose:** Vehicle insurance cover note management

**Insurance Features:**
- Policy tracking and renewals
- Premium calculations
- Claims management
- Compliance monitoring

#### 18. Parking Bill
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/parking_bill/`
**Type:** Transaction DocType
**Purpose:** Vehicle parking fee tracking and payment

**Parking Management:**
- Automatic bill generation
- Payment tracking
- Vehicle-specific parking history
- Integration with fleet management

#### 19. Vehicle Fine Record
**Location:** `apps/csf_tz/csf_tz/csf_tz/doctype/vehicle_fine_record/`
**Type:** Transaction DocType
**Purpose:** Traffic fine tracking and payment management

**Fine Management:**
- Fine recording and tracking
- Payment processing
- Driver assignment
- Compliance reporting

## Module 2: Fleet Management (45+ DocTypes)

### Core Vehicle Management

#### 1. Transport Request
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/transport_request/`
**Type:** Transaction DocType
**Purpose:** Vehicle transportation request management

**Request Features:**
- Trip planning and scheduling
- Vehicle assignment
- Route optimization
- Cost estimation

#### 2. Transport Assignment
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/transport_assignment/`
**Type:** Transaction DocType
**Purpose:** Vehicle and driver assignment to transport requests

**Assignment Features:**
- Driver assignment
- Vehicle allocation
- Schedule management
- Resource optimization

#### 3. Vehicle Trip
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_trip/`
**Type:** Transaction DocType
**Purpose:** Complete vehicle trip tracking and management

**Trip Management:**
- Trip planning and execution
- Real-time location updates
- Fuel consumption tracking
- Performance monitoring

#### 4. Vehicle Log
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_log/`
**Type:** Transaction DocType
**Purpose:** Daily vehicle usage logging

**Logging Features:**
- Odometer readings
- Fuel consumption
- Driver assignments
- Trip summaries

#### 5. Trailer
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/trailer/`
**Type:** Master DocType
**Purpose:** Trailer management and tracking

**Trailer Features:**
- Trailer specifications
- Maintenance scheduling
- Assignment tracking
- Compliance monitoring

### Vehicle Inspection System

#### 6. Vehicle Inspection
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_inspection/`
**Type:** Transaction DocType
**Purpose:** Comprehensive vehicle inspection management

**Inspection Features:**
- Scheduled inspections
- Checklist-based inspections
- Defect tracking
- Compliance reporting

#### 7. Vehicle Inspection Template
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_inspection_template/`
**Type:** Master DocType
**Purpose:** Standardized inspection templates

**Template Features:**
- Customizable inspection checklists
- System-specific templates
- Compliance standards
- Quality control

#### 8. Vehicle Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_checklist/`
**Type:** Transaction DocType
**Purpose:** Daily vehicle safety and condition checks

**Checklist Features:**
- Pre-trip inspections
- Safety checks
- Condition monitoring
- Issue reporting

#### 9. Vehicle Routine Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_routine_checklist/`
**Type:** Transaction DocType
**Purpose:** Routine maintenance checklist management

### Vehicle System Inspection DocTypes

#### 10. Engine Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/engine_checklist/`
**Type:** Child Table DocType
**Purpose:** Engine system inspection checklist

#### 11. Engine Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/engine_details/`
**Type:** Child Table DocType
**Purpose:** Detailed engine inspection results

#### 12. Brake Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/brake_checklist/`
**Type:** Child Table DocType
**Purpose:** Brake system inspection checklist

#### 13. Brake System Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/brake_system_details/`
**Type:** Child Table DocType
**Purpose:** Detailed brake system inspection results

#### 14. Electrical Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/electrical_checklist/`
**Type:** Child Table DocType
**Purpose:** Electrical system inspection checklist

#### 15. Electrical Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/electrical_details/`
**Type:** Child Table DocType
**Purpose:** Detailed electrical system inspection results

#### 16. Fuel System Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/fuel_system_checklist/`
**Type:** Child Table DocType
**Purpose:** Fuel system inspection checklist

#### 17. Fuel System Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/fuel_system_details/`
**Type:** Child Table DocType
**Purpose:** Detailed fuel system inspection results

#### 18. Tire Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/tire_checklist/`
**Type:** Child Table DocType
**Purpose:** Tire condition inspection checklist

#### 19. Tire Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/tire_details/`
**Type:** Child Table DocType
**Purpose:** Detailed tire inspection results

#### 20. Suspension Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/suspension_checklist/`
**Type:** Child Table DocType
**Purpose:** Suspension system inspection checklist

#### 21. Suspension Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/suspension_details/`
**Type:** Child Table DocType
**Purpose:** Detailed suspension system inspection results

#### 22. Steering Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/steering_checklist/`
**Type:** Child Table DocType
**Purpose:** Steering system inspection checklist

#### 23. Steering Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/steering_details/`
**Type:** Child Table DocType
**Purpose:** Detailed steering system inspection results

#### 24. Lighting Checklist
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/lighting_checklist/`
**Type:** Child Table DocType
**Purpose:** Vehicle lighting system inspection checklist

#### 25. Lighting Checklist Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/lighting_checklist_details/`
**Type:** Child Table DocType
**Purpose:** Detailed lighting system inspection results

### Fleet Operations Management

#### 26. Fuel Request
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/fuel_request/`
**Type:** Transaction DocType
**Purpose:** Vehicle fuel request and approval management

**Fuel Management:**
- Fuel request processing
- Approval workflow
- Consumption tracking
- Cost management

#### 27. Fuel Request Table
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/fuel_request_table/`
**Type:** Child Table DocType
**Purpose:** Fuel request line items

#### 28. Expense
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/expense/`
**Type:** Transaction DocType
**Purpose:** Vehicle-related expense tracking

**Expense Categories:**
- Maintenance costs
- Fuel expenses
- Insurance premiums
- Registration fees

#### 29. Fixed Expense
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/fixed_expense/`
**Type:** Master DocType
**Purpose:** Recurring vehicle expense management

#### 30. Fixed Expense Table
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/fixed_expense_table/`
**Type:** Child Table DocType
**Purpose:** Fixed expense line items

### Route and Trip Management

#### 31. Trip Route
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/trip_route/`
**Type:** Master DocType
**Purpose:** Predefined route management

**Route Features:**
- Route planning and optimization
- Distance calculations
- Time estimates
- Cost projections

#### 32. Trip Location
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/trip_location/`
**Type:** Master DocType
**Purpose:** Trip location master data

#### 33. Trip Location Type
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/trip_location_type/`
**Type:** Master DocType
**Purpose:** Classification of trip locations

#### 34. Vehicle Trip Location Update
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_trip_location_update/`
**Type:** Transaction DocType
**Purpose:** Real-time vehicle location tracking

#### 35. Route Steps Table
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/route_steps_table/`
**Type:** Child Table DocType
**Purpose:** Detailed route step information

#### 36. Trip Steps Table
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/trip_steps_table/`
**Type:** Child Table DocType
**Purpose:** Trip execution step tracking

#### 37. Subtrips Table
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/subtrips_table/`
**Type:** Child Table DocType
**Purpose:** Sub-trip management within main trips

### Vehicle Documentation and Equipment

#### 38. Vehicle Documents
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_documents/`
**Type:** Child Table DocType
**Purpose:** Vehicle document management

#### 39. Vehicle Documents Type
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_documents_type/`
**Type:** Master DocType
**Purpose:** Types of vehicle documents

**Document Types:**
- Registration certificates
- Insurance policies
- Inspection certificates
- Driver licenses

#### 40. Vehicle Type
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_type/`
**Type:** Master DocType
**Purpose:** Vehicle classification and specifications

#### 41. Equipment Set
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/equipment_set/`
**Type:** Master DocType
**Purpose:** Vehicle equipment set management

#### 42. Equipment Table
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/equipment_table/`
**Type:** Child Table DocType
**Purpose:** Equipment inventory tracking

#### 43. Unit of Measure
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/unit_of_measure/`
**Type:** Master DocType
**Purpose:** Fleet-specific units of measurement

#### 44. Delivery Note Template
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/delivery_note_template/`
**Type:** Master DocType
**Purpose:** Standardized delivery note templates

#### 45. Assigned Transport Details
**Location:** `apps/csf_tz/csf_tz/fleet_management/doctype/assigned_transport_details/`
**Type:** Child Table DocType
**Purpose:** Transport assignment details

## Module 3: Clearing and Forwarding (25+ DocTypes)

### Import/Export Management

#### 1. Container
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container/`
**Type:** Master DocType
**Purpose:** Container management for import/export operations

**Container Features:**
- Container specifications
- Tracking and monitoring
- Status management
- Cost allocation

#### 2. Container Receipt
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container_receipt/`
**Type:** Transaction DocType
**Purpose:** Container receipt and processing

#### 3. Clearing Agent
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/clearing_agent/`
**Type:** Master DocType
**Purpose:** Clearing agent management

#### 4. Clearing Job
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/clearing_job/`
**Type:** Transaction DocType
**Purpose:** Complete clearing job management

**Job Features:**
- Job planning and execution
- Document management
- Cost tracking
- Timeline management

#### 5. Clearing Job Item
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/clearing_job_item/`
**Type:** Child Table DocType
**Purpose:** Items within clearing jobs

#### 6. Clearing Job Container
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/clearing_job_container/`
**Type:** Child Table DocType
**Purpose:** Containers within clearing jobs

### Customs and Documentation

#### 7. Border
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/border/`
**Type:** Master DocType
**Purpose:** Border crossing point management

#### 8. Border Processing
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/border_processing/`
**Type:** Transaction DocType
**Purpose:** Border processing and customs clearance

#### 9. Bond
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/bond/`
**Type:** Master DocType
**Purpose:** Customs bond management

#### 10. Bond Processing
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/bond_processing/`
**Type:** Transaction DocType
**Purpose:** Bond processing and management

#### 11. Manifest
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/manifest/`
**Type:** Transaction DocType
**Purpose:** Cargo manifest management

#### 12. Manifest Item
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/manifest_item/`
**Type:** Child Table DocType
**Purpose:** Items within cargo manifests

### Shipping and Logistics

#### 13. Shipping Line
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/shipping_line/`
**Type:** Master DocType
**Purpose:** Shipping line management

#### 14. Port
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/port/`
**Type:** Master DocType
**Purpose:** Port and terminal management

#### 15. Warehouse Receipt
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/warehouse_receipt/`
**Type:** Transaction DocType
**Purpose:** Warehouse receipt processing

#### 16. Warehouse Receipt Item
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/warehouse_receipt_item/`
**Type:** Child Table DocType
**Purpose:** Items within warehouse receipts

### Financial and Billing

#### 17. Clearing Charges
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/clearing_charges/`
**Type:** Master DocType
**Purpose:** Clearing service charges management

#### 18. Clearing Invoice
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/clearing_invoice/`
**Type:** Transaction DocType
**Purpose:** Clearing service invoicing

#### 19. Clearing Invoice Item
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/clearing_invoice_item/`
**Type:** Child Table DocType
**Purpose:** Invoice line items

#### 20. Exchange Rate
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/exchange_rate/`
**Type:** Master DocType
**Purpose:** Currency exchange rate management

### Documentation and Compliance

#### 21. Import Declaration
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/import_declaration/`
**Type:** Transaction DocType
**Purpose:** Import declaration processing

#### 22. Export Declaration
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/export_declaration/`
**Type:** Transaction DocType
**Purpose:** Export declaration processing

#### 23. Bill of Lading
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/bill_of_lading/`
**Type:** Transaction DocType
**Purpose:** Bill of lading management

#### 24. Commercial Invoice
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/commercial_invoice/`
**Type:** Transaction DocType
**Purpose:** Commercial invoice processing

#### 25. Packing List
**Location:** `apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/packing_list/`
**Type:** Transaction DocType
**Purpose:** Cargo packing list management

## Module 4: After Sales Services (12+ DocTypes)

### Service Management

#### 1. Machine Strip Request
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/machine_strip_request/`
**Type:** Transaction DocType
**Purpose:** Equipment maintenance and stripping requests

#### 2. Pre Delivery Inspection
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/pre_delivery_inspection/`
**Type:** Transaction DocType
**Purpose:** Quality control before delivery

#### 3. Maintenance Schedule
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/maintenance_schedule/`
**Type:** Master DocType
**Purpose:** Preventive maintenance scheduling

#### 4. Service Request
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/service_request/`
**Type:** Transaction DocType
**Purpose:** Customer service request management

#### 5. Service Type
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/service_type/`
**Type:** Master DocType
**Purpose:** Classification of service types

#### 6. Warranty Claim
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/warranty_claim/`
**Type:** Transaction DocType
**Purpose:** Product warranty claim processing

#### 7. Requested Payments
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/requested_payments/`
**Type:** Transaction DocType
**Purpose:** Service payment request management

#### 8. Service Report
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/service_report/`
**Type:** Transaction DocType
**Purpose:** Service completion reporting

#### 9. Spare Parts Request
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/spare_parts_request/`
**Type:** Transaction DocType
**Purpose:** Spare parts ordering and management

#### 10. Customer Feedback
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/customer_feedback/`
**Type:** Transaction DocType
**Purpose:** Post-service customer feedback collection

#### 11. Service Agreement
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/service_agreement/`
**Type:** Master DocType
**Purpose:** Service contract management

#### 12. Technician Assignment
**Location:** `apps/csf_tz/csf_tz/after_sales_services/doctype/technician_assignment/`
**Type:** Transaction DocType
**Purpose:** Technician assignment to service requests

## Module 5: Workshop Management (8+ DocTypes)

### Workshop Operations

#### 1. Workshop Service
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_service/`
**Type:** Transaction DocType
**Purpose:** Workshop service management

#### 2. Workshop Service Type
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_service_type/`
**Type:** Master DocType
**Purpose:** Workshop service classification

#### 3. Workshop Item
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_item/`
**Type:** Master DocType
**Purpose:** Workshop inventory management

#### 4. Workshop Request
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_request/`
**Type:** Transaction DocType
**Purpose:** Workshop service request processing

#### 5. Workshop Equipment
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_equipment/`
**Type:** Master DocType
**Purpose:** Workshop equipment management

#### 6. Workshop Schedule
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_schedule/`
**Type:** Transaction DocType
**Purpose:** Workshop scheduling and planning

#### 7. Workshop Invoice
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_invoice/`
**Type:** Transaction DocType
**Purpose:** Workshop service billing

#### 8. Workshop Report
**Location:** `apps/csf_tz/csf_tz/workshop/doctype/workshop_report/`
**Type:** Transaction DocType
**Purpose:** Workshop service reporting

## Module 6: Sales and Marketing (8+ DocTypes)

### Customer Management

#### 1. Customer Communication
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/customer_communication/`
**Type:** Transaction DocType
**Purpose:** Customer communication tracking

#### 2. Customer Alert
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/customer_alert/`
**Type:** Transaction DocType
**Purpose:** Customer alert and notification management

#### 3. Past Sales Analysis
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/past_sales_analysis/`
**Type:** Report DocType
**Purpose:** Historical sales data analysis

#### 4. Sales Target
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/sales_target/`
**Type:** Master DocType
**Purpose:** Sales target setting and tracking

#### 5. Customer Payment Plan
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/customer_payment_plan/`
**Type:** Master DocType
**Purpose:** Customer payment schedule management

#### 6. Marketing Campaign
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/marketing_campaign/`
**Type:** Master DocType
**Purpose:** Marketing campaign management

#### 7. Lead Source
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/lead_source/`
**Type:** Master DocType
**Purpose:** Lead source tracking and analysis

#### 8. Customer Segment
**Location:** `apps/csf_tz/csf_tz/sales_and_marketing/doctype/customer_segment/`
**Type:** Master DocType
**Purpose:** Customer segmentation and classification

## Module 7: Purchase and Stock Management (6+ DocTypes)

### Enhanced Inventory Management

#### 1. Bin Management
**Location:** `apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/bin_management/`
**Type:** Master DocType
**Purpose:** Warehouse bin organization and management

#### 2. Purchase Order Tracking
**Location:** `apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/purchase_order_tracking/`
**Type:** Transaction DocType
**Purpose:** Enhanced purchase order monitoring

#### 3. Stock Movement Analysis
**Location:** `apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/stock_movement_analysis/`
**Type:** Report DocType
**Purpose:** Stock movement analytics and reporting

#### 4. Supplier Performance
**Location:** `apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/supplier_performance/`
**Type:** Report DocType
**Purpose:** Supplier performance evaluation

#### 5. Item Reorder Level
**Location:** `apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/item_reorder_level/`
**Type:** Master DocType
**Purpose:** Automated reorder level management

#### 6. Stock Reconciliation Template
**Location:** `apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/stock_reconciliation_template/`
**Type:** Master DocType
**Purpose:** Standardized stock reconciliation templates

## Module 8: Meal Count (5+ DocTypes)

### Employee Meal Management

#### 1. Biometric Device
**Location:** `apps/csf_tz/csf_tz/meal_count/doctype/biometric_device/`
**Type:** Master DocType
**Purpose:** Biometric device configuration and management

#### 2. Meal Type
**Location:** `apps/csf_tz/csf_tz/meal_count/doctype/meal_type/`
**Type:** Master DocType
**Purpose:** Meal category and type management

#### 3. Employee Meal Record
**Location:** `apps/csf_tz/csf_tz/meal_count/doctype/employee_meal_record/`
**Type:** Transaction DocType
**Purpose:** Employee meal consumption tracking

#### 4. Meal Count Settings
**Location:** `apps/csf_tz/csf_tz/meal_count/doctype/meal_count_settings/`
**Type:** Single DocType
**Purpose:** Meal counting system configuration

#### 5. Meal Report
**Location:** `apps/csf_tz/csf_tz/meal_count/doctype/meal_report/`
**Type:** Report DocType
**Purpose:** Meal consumption reporting and analytics

## Module 9: Feedback System (4+ DocTypes)

### Customer Feedback Management

#### 1. Feedback Form
**Location:** `apps/csf_tz/csf_tz/feedback/doctype/feedback_form/`
**Type:** Master DocType
**Purpose:** Customizable feedback form creation

#### 2. Feedback Template
**Location:** `apps/csf_tz/csf_tz/feedback/doctype/feedback_template/`
**Type:** Master DocType
**Purpose:** Standardized feedback templates

#### 3. Feedback Response
**Location:** `apps/csf_tz/csf_tz/feedback/doctype/feedback_response/`
**Type:** Transaction DocType
**Purpose:** Customer feedback response collection

#### 4. Feedback Analysis
**Location:** `apps/csf_tz/csf_tz/feedback/doctype/feedback_analysis/`
**Type:** Report DocType
**Purpose:** Feedback data analysis and reporting

## Module 10: Stanbic Bank Integration (3+ DocTypes)

### Banking Integration

#### 1. Stanbic Payment
**Location:** `apps/csf_tz/csf_tz/stanbic/doctype/stanbic_payment/`
**Type:** Transaction DocType
**Purpose:** Stanbic Bank payment processing

#### 2. Stanbic Settings
**Location:** `apps/csf_tz/csf_tz/stanbic/doctype/stanbic_settings/`
**Type:** Single DocType
**Purpose:** Stanbic Bank integration configuration

#### 3. Stanbic Transaction Log
**Location:** `apps/csf_tz/csf_tz/stanbic/doctype/stanbic_transaction_log/`
**Type:** Transaction DocType
**Purpose:** Banking transaction audit trail

## Module 11: AI Integration (2+ DocTypes)

### Artificial Intelligence Features

#### 1. LLM Settings
**Location:** `apps/csf_tz/csf_tz/ai_integration/doctype/llm_settings/`
**Type:** Single DocType
**Purpose:** Large Language Model configuration

#### 2. OpenAI Query Log
**Location:** `apps/csf_tz/csf_tz/ai_integration/doctype/openai_query_log/`
**Type:** Transaction DocType
**Purpose:** AI query tracking and logging

## Module 12: Auth OTP (2+ DocTypes)

### Authentication System

#### 1. AuthOTP Settings
**Location:** `apps/csf_tz/csf_tz/authotp/doctype/authotp_settings/`
**Type:** Single DocType
**Purpose:** OTP authentication configuration

#### 2. OTP Register
**Location:** `apps/csf_tz/csf_tz/authotp/doctype/otp_register/`
**Type:** Transaction DocType
**Purpose:** OTP generation and validation tracking

## DocType Relationships and Dependencies

### Master-Transaction Relationships

#### Geographic Hierarchy
```
TZ Region (Master)
├── TZ District (Master)
    ├── TZ Ward (Master)
        └── TZ Village (Master)
```

#### Fleet Management Hierarchy
```
Vehicle Type (Master)
├── Transport Request (Transaction)
    ├── Transport Assignment (Transaction)
        └── Vehicle Trip (Transaction)
            └── Vehicle Log (Transaction)
```

#### Clearing and Forwarding Workflow
```
Clearing Job (Transaction)
├── Clearing Job Container (Child Table)
├── Clearing Job Item (Child Table)
└── Clearing Invoice (Transaction)
    └── Clearing Invoice Item (Child Table)
```

#### Service Management Flow
```
Service Request (Transaction)
├── Technician Assignment (Transaction)
├── Service Report (Transaction)
└── Customer Feedback (Transaction)
```

### Key Integration Points

#### HR and Payroll Integration
- **Piecework** → **Additional Salary Component** → **Payroll Processing**
- **Employee Meal Record** → **Payroll Deductions**
- **Overtime Calculation** → **Salary Slip Generation**

#### Financial Integration
- **Bank Charges** → **Payment Entry** → **GL Entries**
- **Withholding Tax** → **Purchase Invoice** → **Tax Compliance**
- **Clearing Charges** → **Clearing Invoice** → **Customer Billing**

#### Inventory Integration
- **Stock Transfer** → **Stock Entry** → **GL Entries**
- **Delivery Exchange** → **Sales Return** → **Stock Adjustment**
- **Trade-in Processing** → **Asset Management** → **Depreciation**

## DocType Categories Summary

### By Functionality

#### Master Data DocTypes (80+)
- Geographic data (Regions, Districts, Wards, Villages)
- Vehicle and equipment specifications
- Service types and categories
- Customer and supplier classifications
- Product and inventory masters

#### Transaction DocTypes (100+)
- Sales and purchase transactions
- HR and payroll transactions
- Fleet management operations
- Service requests and maintenance
- Financial transactions and payments

#### Configuration DocTypes (20+)
- System settings and preferences
- Integration configurations
- Workflow settings
- User access controls

#### Reporting DocTypes (15+)
- Financial reports and analytics
- Operational reports
- Compliance reports
- Performance dashboards

### By Business Process

#### Core Business Operations (120+)
- Sales and customer management
- Purchase and supplier management
- Inventory and warehouse management
- Financial management and accounting

#### Specialized Operations (80+)
- Fleet management and logistics
- Clearing and forwarding
- Workshop and maintenance services
- After-sales service management

#### Support Functions (20+)
- Human resources and payroll
- System administration
- Security and authentication
- Reporting and analytics

## Technical Implementation

### DocType Design Patterns

#### Naming Conventions
- **Auto-naming:** Format-based naming for transactions
- **Prompt Naming:** User-defined names for masters
- **Field-based Naming:** Using specific field values

#### Field Types Usage
- **Link Fields:** Extensive use for relationships
- **Child Tables:** Complex data structures
- **Calculated Fields:** Business logic implementation
- **Conditional Fields:** Dynamic form behavior

#### Permissions and Security
- **Role-based Access:** Granular permission control
- **Document-level Security:** Row-level security implementation
- **Field-level Permissions:** Sensitive data protection
- **Workflow Integration:** Approval-based processing

### Performance Considerations

#### Database Optimization
- **Indexed Fields:** Optimized query performance
- **Selective Loading:** Reduced memory usage
- **Caching Strategies:** Improved response times
- **Archive Policies:** Data lifecycle management

#### User Experience
- **Form Optimization:** Streamlined data entry
- **List View Customization:** Efficient data browsing
- **Search Enhancement:** Quick data location
- **Mobile Responsiveness:** Cross-device compatibility

## Maintenance and Development

### DocType Lifecycle Management

#### Development Process
1. **Requirements Analysis:** Business need identification
2. **Design Phase:** DocType structure planning
3. **Implementation:** Field and logic development
4. **Testing:** Functionality validation
5. **Deployment:** Production release
6. **Maintenance:** Ongoing support and updates

#### Version Control
- **Schema Changes:** Tracked through patches
- **Data Migration:** Automated upgrade scripts
- **Backward Compatibility:** Legacy system support
- **Documentation Updates:** Synchronized with changes

### Best Practices

#### DocType Design
- **Single Responsibility:** Each DocType serves one purpose
- **Normalized Structure:** Avoid data duplication
- **Extensible Design:** Future enhancement capability
- **Performance Optimization:** Efficient data access

#### Business Logic Implementation
- **Server-side Validation:** Data integrity enforcement
- **Client-side Enhancement:** User experience improvement
- **Workflow Integration:** Process automation
- **Audit Trail:** Change tracking and compliance

## Related Documentation
- [CSF TZ Custom Fields Documentation](CSF_TZ_Custom_Fields_Documentation.md)
- [CSF TZ Property Setters Documentation](CSF_TZ_Property_Setters_Documentation.md)
- [CSF TZ Hooks Documentation](CSF_TZ_Hooks_Documentation.md)
- [CSF TZ Complete Documentation](CSF_TZ_Complete_Documentation.md)

## Conclusion

The CSF TZ application represents one of the most comprehensive DocType implementations in the Frappe ecosystem, with over 200 custom DocTypes spanning 12 major business modules. This extensive collection addresses virtually every aspect of business operations in Tanzania, from basic accounting and HR to specialized functions like fleet management, clearing and forwarding, and workshop operations.

The modular design ensures scalability and maintainability while providing businesses with the flexibility to implement only the modules they need. The deep integration between modules creates a cohesive business management system that can handle complex, multi-faceted operations while maintaining data integrity and business process compliance.